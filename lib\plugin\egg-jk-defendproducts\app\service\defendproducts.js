const Service = require('egg').Service;
// const fs = require('fs');
const path = require('path');
const moment = require('moment');
const _ = require('lodash');

class DefendProductsService extends Service {
  async get(params) {
    const { ctx } = this;
    try {
      const EnterpriseID = ctx.session.adminUserInfo
        ? ctx.session.adminUserInfo.EnterpriseID
        : '';
      // console.log(EnterpriseID, 'EnterpriseID');
      // const res = await ctx.model.Defendproducts.findOne({ EnterpriseID });
      const res = await ctx.service.db.aggregate('Defendproducts', [
        { $match: { EnterpriseID } },
        { $project: { invoice: 1, certifications: 1, receiveForm: 1 } },
      ]);
      const pipeline = [
        { $match: { EnterpriseID } },
        { $project: { formData: 1 } },
        { $unwind: '$formData' },
        {
          $match: {
            'formData.date': {
              $gte: new Date(new Date(params.min).getTime() - 28800000),
              $lt: new Date(new Date(params.max).getTime() - 28800000),
            },
          },
        },
        { $sort: { 'formData.date': -1 } },
        { $skip: (params.current - 1) * (params.pageSize || 10) },
        { $limit: parseInt(params.pageSize) || 1000 },
      ];
      const Tdata = await ctx.service.db.aggregate('Defendproducts', pipeline);
      // console.log(Tdata, 'Tdata');
      const array = [];
      Tdata.forEach(item => {
        array.push(item.formData);
      });
      const pipelineLen = [
        { $match: { EnterpriseID } },
        { $unwind: '$formData' },
        {
          $match: {
            'formData.date': {
              $gte: new Date(new Date(params.min).getTime() - 28800000),
              $lt: new Date(new Date(params.max).getTime() - 28800000),
            },
          },
        },
        { $count: 'totalLength' },
      ];
      const totalLength = await ctx.service.db.aggregate(
        'Defendproducts',
        pipelineLen
      );
      return {
        res: res[0],
        array,
        totalLength: totalLength[0] ? totalLength[0].totalLength : 0,
      };
    } catch (error) {
      throw error;
    }
  }
  async add(params) {
    // console.log(params, 'add11111111111111111111');
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo
      ? ctx.session.adminUserInfo.EnterpriseID
      : '';
    const data = {
      formData: [],
      EnterpriseID,
      invoice: params.invoice,
      certifications: params.certifications,
      receiveForm: params.receiveForm,
    };
    try {
      const database = await ctx.service.db.findOne('Defendproducts', {
        EnterpriseID,
      });
      if (database === null) {
        const res = await new ctx.service.db.create('Defendproducts', {
          formData: [],
          EnterpriseID,
          invoice: data.invoice,
          certifications: data.certifications,
          receiveForm: data.receiveForm,
        });
        return res;
      }
      const res = await ctx.service.db.updateOne(
        'Defendproducts',
        { EnterpriseID },
        {
          $set: {
            invoice: data.invoice,
            certifications: data.certifications,
            receiveForm: data.receiveForm,
          },
        }
      );
      await this.updateFilesCompleteness();
      return res;
    } catch (error) {
      throw error;
    }
  }
  async time(params) {
    const { ctx } = this;
    try {
      const EnterpriseID = ctx.session.adminUserInfo
        ? ctx.session.adminUserInfo.EnterpriseID
        : '';
      params.EnterpriseID = EnterpriseID;
      let res;
      const obj = await ctx.service.db.findOne('Defendproducts', {
        EnterpriseID: params.EnterpriseID,
      });
      if (!obj) {
        await ctx.service.db.create('Defendproducts', {
          EnterpriseID: params.EnterpriseID,
        });
      } else if (!obj.oldtime || obj.oldtime !== params.newtime) {
        await ctx.service.db.updateOne(
          'Defendproducts',
          { EnterpriseID: params.EnterpriseID },
          { oldtime: params.newtime }
        );
        res = true;
      } else {
        res = false;
      }
      return res;
    } catch (error) {
      throw error;
    }
  }
  async addexcel(params) {
    const { ctx } = this;
    try {
      const data = JSON.parse(params.formData);
      const EnterpriseID = this.ctx.session.adminUserInfo
        ? this.ctx.session.adminUserInfo.EnterpriseID
        : '';
      const isdata = await ctx.service.db.findOne('Defendproducts', {
        EnterpriseID,
      });
      if (isdata.length > 0) {
        await ctx.service.db.updateOne(
          'Defendproducts',
          { EnterpriseID },
          { $push: { formData: { $each: data } } }
        );
      } else {
        await ctx.service.db.create('Defendproducts', {
          EnterpriseID,
          formData: data,
        });
      }
      await this.updateFilesCompleteness();
    } catch (error) {
      throw error;
    }
  }
  async update(params) {
    // (看遍逻辑)
    const { ctx } = this;
    try {
      console.log(params);
      const EnterpriseID = ctx.session.adminUserInfo
        ? ctx.session.adminUserInfo.EnterpriseID
        : '';
      params.EnterpriseID = EnterpriseID;
      const res = await ctx.service.db.updateMany(
        'Defendproducts',
        {},
        params,
        { upsert: true }
      );
      await this.updateFilesCompleteness();
      console.log(res, 'update222222222222222');
    } catch (error) {
      throw error;
    }
  }

  async updateSingle(params) {
    const { ctx } = this;
    try {
      const EnterpriseID = ctx.session.adminUserInfo
        ? ctx.session.adminUserInfo.EnterpriseID
        : '';
      params.forEach(async item => {
        if (item._id && item._id.trim() !== '') {
          await ctx.service.db.updateOne(
            'Defendproducts',
            { EnterpriseID, 'formData._id': item._id },
            {
              $set: {
                'formData.$': item,
              },
            }
          );
        } else {
          // 新增时删除空的_id字段
          if (item._id === '' || item._id === null || item._id === undefined) {
            delete item._id;
          }
          await ctx.service.db.updateMany(
            'Defendproducts',
            { EnterpriseID },
            {
              $push: {
                formData: item,
              },
            }
          );
        }
      });
      await this.updateFilesCompleteness();
    } catch (error) {
      console.log(error);
    }
  }
  async delete(params) {
    const { ctx } = this;
    try {
      const EnterpriseID = ctx.session.adminUserInfo
        ? ctx.session.adminUserInfo.EnterpriseID
        : '';
      const res = await ctx.service.db.updateOne(
        'Defendproducts',
        { EnterpriseID },
        { $pull: { formData: { _id: params._id } } }
      );
      await this.updateFilesCompleteness();
      console.log(res);
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
  async deleteTables(params) {
    try {
      const { ctx } = this;
      const EnterpriseID = ctx.session.adminUserInfo
        ? ctx.session.adminUserInfo.EnterpriseID
        : '';
      await ctx.service.db.updateOne(
        'Defendproducts',
        { EnterpriseID },
        {
          $pull: {
            formData: {
              _id: {
                $in: params,
              },
            },
          },
        }
      );
      await this.updateFilesCompleteness();
    } catch (error) {
      console.log(error);
    }
  }
  async deletefile(params) {
    const { ctx } = this;
    try {
      if (params.invoice) {
        const res1 = await ctx.service.db.updateOne(
          'Defendproducts',
          { 'invoice._id': params._id },
          { $pull: { invoice: { webname: params.invoice } } }
        );
        console.log(res1);
        const EnterpriseID = ctx.session.adminUserInfo
          ? ctx.session.adminUserInfo.EnterpriseID
          : '';

        this.deleteFileReal(
          params.invoice,
          `${this.config.upload_path}/${EnterpriseID}`
        );
      } else if (params.certifications) {
        const res2 = await ctx.service.db.updateOne(
          'Defendproducts',
          { 'certifications._id': params._id },
          { $pull: { certifications: { webname: params.certifications } } }
        );
        console.log(res2);
        const EnterpriseID = ctx.session.adminUserInfo
          ? ctx.session.adminUserInfo.EnterpriseID
          : '';
        this.deleteFileReal(
          params.certifications,
          `${this.config.upload_path}/${EnterpriseID}`
        );
        console.log(this.config.upload_path);
      } else if (params.receiveForm) {
        const res2 = await ctx.service.db.updateOne(
          'Defendproducts',
          { 'receiveForm._id': params._id },
          { $pull: { receiveForm: { webname: params.receiveForm } } }
        );
        console.log(res2);
        const EnterpriseID = ctx.session.adminUserInfo
          ? ctx.session.adminUserInfo.EnterpriseID
          : '';
        this.deleteFileReal(
          params.receiveForm,
          `${this.config.upload_path}/${EnterpriseID}`
        );
        console.log(this.config.upload_path);
      }
      await this.updateFilesCompleteness();
    } catch (error) {
      throw error;
    }
  }
  deleteFileReal(fileName, configfilePath) {
    console.log(fileName);
    console.log(configfilePath);
    this.ctx.helper.deleteObject(path.resolve(configfilePath, fileName));
  }
  async updateFilesCompleteness() {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo
      ? ctx.session.adminUserInfo.EnterpriseID
      : '';
    const data = await ctx.service.db.findOne('Defendproducts', {
      EnterpriseID,
    });
    const date = [];
    if (data.receiveForm.length || data.formData.length) {
      if (data.receiveForm.length > 0) {
        for (let i = 0; i < data.receiveForm.length; i++) {
          const ele = data.receiveForm[i];
          const time = `${ele.date.slice(0, 4)}-${ele.date.slice(-3, -1)}`;
          date.push(new Date(time));
        }
      }
      if (data.formData.length > 0) {
        for (let i = 0; i < data.formData.length; i++) {
          const ele = data.formData[i];
          date.push(ele.date);
        }
      }
      date.sort(function(a, b) {
        return b - a;
      });
      ctx.service.filesCompleteness.update({
        defendproducts: {
          completion: 100,
          validPeriod: moment(date[0]).add(1, 'y'),
        },
      });
    }
  }

  async saveDefendProductList(data, importType, warehouseId) { // 保存企业的防护用品清单
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';

    // 如果没有提供仓库ID，获取公共仓库ID
    if (!warehouseId) {
      const publicWarehouse = await ctx.model.Warehouse.findOne({
        EnterpriseID,
        isPublic: true,
      });
      if (!publicWarehouse) {
        throw new Error('未找到公共仓库，请联系管理员初始化仓库');
      }
      warehouseId = publicWarehouse._id;
    }

    console.log('saveDefendProductList - 仓库ID:', warehouseId);

    // 根据企业ID和仓库ID查找现有清单
    const query = { EnterpriseID, warehouseId };
    const oldList = await ctx.service.db.findOne('ProtectiveSuppliesList', query);
    const listData = [];
    for (const key in data) {
      const tableHeader = [
        'materialCode',
        'product',
        'productSpec',
        'modelNumber',
        'style',
        'protectionType',
        'useMethod',
        'industryEnvironment',
        'harmFactors',
        'surplus',
      ];
      const item = {
        name: key,
        tableHeader,
        data: data[key],
      };
      listData.push(item);
    }

    if (!oldList) {
      await ctx.service.db.create('ProtectiveSuppliesList', { EnterpriseID, warehouseId, list: listData });
    } else {
      for (let i = 0; i < listData.length; i++) {
        const listItem = listData[i];
        // 判断该分类是否已存在，不存在的就直接创建
        const targetList = oldList.list.find(e => e.name === listItem.name);

        // 如果存在该分类，就对里面的防护用品更新
        if (targetList) {
          for (let j = 0; j < listItem.data.length; j++) {
            const item = listItem.data[j];
            const targetItem = targetList.data.find(
              e => e.materialCode === item.materialCode
            );
            if (targetItem) {
              const surplus =
                parseInt(item.surplus) + parseInt(targetItem.surplus);
              await ctx.service.db.updateOne(
                'ProtectiveSuppliesList',
                { _id: oldList._id },
                { $set: { 'list.$[i].data.$[j].surplus': surplus } },
                {
                  arrayFilters: [
                    { 'i._id': targetList._id },
                    { 'j._id': targetItem._id },
                  ],
                }
              );
            } else {
              await ctx.service.db.updateOne(
                'ProtectiveSuppliesList',
                { _id: oldList._id },
                { $push: { 'list.$[i].data': item } },
                { arrayFilters: [{ 'i._id': targetList._id }] }
              );
            }
          }
        } else {
          await ctx.service.db.updateOne(
            'ProtectiveSuppliesList',
            { _id: oldList._id },
            { $push: { list: listItem } }
          );
        }
      }
    }
  }

  async delOneprotection({ categoryId, dataId }) {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo
      ? ctx.session.adminUserInfo.EnterpriseID
      : '';
    await ctx.service.db.updateOne(
      'ProtectiveSuppliesList',
      { EnterpriseID },
      { $pull: { 'list.$[t].data': { _id: dataId } } },
      { arrayFilters: [{ 't._id': categoryId }] }
    );
    return '删除成功';
  }

  async getDefendProductList(params = {}) { // 获取企业的防护用品清单
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';

    console.log('=== getDefendProductList 调试信息 ===');
    console.log('当前用户企业ID:', EnterpriseID);
    console.log('用户会话信息:', ctx.session.adminUserInfo);

    // 构建查询条件
    const query = { EnterpriseID };

    // 如果指定了仓库ID，添加仓库过滤条件
    if (params.warehouseId) {
      query.warehouseId = params.warehouseId;
    }

    console.log('数据库查询条件:', JSON.stringify(query, null, 2));
    console.log('请求参数:', JSON.stringify(params, null, 2));

    // 先查询所有数据，看看数据库中有什么
    const allData = await ctx.service.db.find('ProtectiveSuppliesList', {});
    console.log('数据库中所有防护用品清单数量:', allData.length);
    if (allData.length > 0) {
      console.log('数据库中的企业ID列表:', allData.map(item => item.EnterpriseID));
    }

    const data = await ctx.service.db.findOne('ProtectiveSuppliesList', query);

    console.log('查询结果:', data ? '找到数据' : '未找到数据');
    if (data) {
      console.log('找到的数据企业ID:', data.EnterpriseID);
      console.log('找到的数据仓库ID:', data.warehouseId);
      console.log('找到的数据分类数量:', data.list ? data.list.length : 0);
    }

    // 如果找到了数据，进行后端筛选
    if (data && data.list) {
      console.log('原始数据分类数量:', data.list.length);
      console.log('原始数据分类列表:', data.list.map(cat => ({
        name: cat.name,
        _id: cat._id,
        dataCount: cat.data ? cat.data.length : 0,
      })));

      // 应用搜索关键词筛选
      if (params.searchKeyword && params.searchKeyword.trim()) {
        const keyword = params.searchKeyword.trim().toLowerCase();
        console.log('应用搜索关键词筛选:', keyword);

        data.list = data.list.filter(category => {
          // 筛选分类名称
          if (category.name && category.name.toLowerCase().includes(keyword)) {
            return true;
          }

          // 筛选分类下的产品
          if (category.data && category.data.length > 0) {
            category.data = category.data.filter(product => {
              return (product.product && product.product.toLowerCase().includes(keyword)) ||
                     (product.materialCode && product.materialCode.toLowerCase().includes(keyword));
            });
            return category.data.length > 0; // 只保留有匹配产品的分类
          }

          return false;
        });

        console.log('搜索筛选后分类数量:', data.list.length);
      }

      // 应用分类筛选
      if (params.categoryId && params.categoryId.trim()) {
        const categoryId = params.categoryId.trim();
        console.log('应用分类ID筛选:', categoryId);

        data.list = data.list.filter(category => {
          // 直接匹配分类ID
          if (category._id === categoryId) {
            return true;
          }

          // 匹配分类下的产品分类ID
          if (category.data && category.data.length > 0) {
            category.data = category.data.filter(product => {
              return product.categoryId === categoryId;
            });
            return category.data.length > 0;
          }

          return false;
        });

        console.log('分类ID筛选后分类数量:', data.list.length);
      }

      // 应用分类路径筛选（支持级联选择）
      if (params.categoryPath && params.categoryPath.length > 0) {
        const categoryPath = params.categoryPath;
        console.log('分类路径筛选 - 请求的分类路径:', categoryPath);

        // 获取分类树数据，用于查找父级分类
        let categoryTree = [];
        try {
          const categoryService = ctx.service.protectionCategory;
          if (categoryService) {
            categoryTree = await categoryService.buildCategoryTree({
              includeSystem: true,
              activeOnly: true,
            });
            console.log('获取到分类树数据，用于父级匹配');
          }
        } catch (error) {
          console.log('获取分类树失败，使用简单匹配:', error.message);
        }

        // 递归查找分类的所有子分类ID
        const getAllChildCategoryIds = (categoryId, tree) => {
          const result = [ categoryId ];
          const findChildren = (id, categories) => {
            for (const category of categories) {
              if (category._id === id && category.children) {
                for (const child of category.children) {
                  result.push(child._id);
                  findChildren(child._id, category.children);
                }
                break;
              }
              if (category.children) {
                findChildren(id, category.children);
              }
            }
          };
          findChildren(categoryId, tree);
          return result;
        };

        // 获取所有匹配的分类ID（包括子分类）
        const allMatchingCategoryIds = new Set();
        for (const categoryId of categoryPath) {
          allMatchingCategoryIds.add(categoryId);
          if (categoryTree.length > 0) {
            const childIds = getAllChildCategoryIds(categoryId, categoryTree);
            childIds.forEach(id => allMatchingCategoryIds.add(id));
          }
        }

        console.log('所有匹配的分类ID:', Array.from(allMatchingCategoryIds));

        data.list = data.list.filter(category => {
          console.log('检查分类:', category.name, '分类ID:', category._id);

          // 匹配分类路径 - 检查当前分类是否在选中的路径中
          if (allMatchingCategoryIds.has(category._id)) {
            console.log('分类匹配成功:', category.name);
            return true;
          }

          // 匹配分类下的产品分类路径
          if (category.data && category.data.length > 0) {
            const originalDataLength = category.data.length;
            category.data = category.data.filter(product => {
              console.log('检查产品:', product.product, '产品分类ID:', product.categoryId);
              // 检查产品的分类ID是否在匹配的分类ID集合中
              const isMatch = product.categoryId && allMatchingCategoryIds.has(product.categoryId);
              if (isMatch) {
                console.log('产品匹配成功:', product.product);
              }
              return isMatch;
            });
            console.log(`分类 ${category.name} 的产品筛选: ${originalDataLength} -> ${category.data.length}`);
            return category.data.length > 0;
          }

          return false;
        });

        console.log('分类路径筛选完成，剩余分类数量:', data.list.length);
      }
    }

    console.log('最终返回数据分类数量:', data ? (data.list ? data.list.length : 0) : 0);
    console.log('=== 调试信息结束 ===');
    return data;
  }

  async saveProtectionPlan(data) {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo
      ? ctx.session.adminUserInfo.EnterpriseID
      : '';
    const oldPlan = await ctx.service.db.findOne('ProtectionPlan', {
      EnterpriseID,
    });
    const planData = [];
    for (let j = 0; j < data.length; j++) {
      const ele = data[j];
      if (ele.workstation && ele.workstation !== '/') {
        const pipeLine = [
          { $match: { EnterpriseID } },
          { $unwind: '$children' },
          {
            $project: {
              mill: {
                $cond: [{ $eq: [ '$category', 'mill' ] }, [ '$name', '$_id' ], []],
              },
              workspace: {
                $cond: [
                  { $eq: [ '$category', 'mill' ] },
                  [ '$children.name', '$children._id' ],
                  [ '$name', '$_id' ],
                ],
              },
              station: {
                $cond: [
                  { $eq: [ '$category', 'mill' ] },
                  '$children.children',
                  [ '$children' ],
                ],
              },
            },
          },
        ];
        const Mill = await ctx.service.db.aggregate(
          'MillConstruction',
          pipeLine
        );
        for (let i = 0; i < Mill.length; i++) {
          const element = Mill[i];
          if (
            element.mill.length === 0 ||
            (element.mill.length > 0 && ele.workshop === element.mill[0])
          ) {
            if (
              ele.workspaces &&
              ele.workspaces !== '/' &&
              element.workspace.length > 0 &&
              ele.workspaces === element.workspace[0]
            ) {
              element.station.forEach(item => {
                if (item.name === ele.workstation) {
                  console.log(
                    ele.workstation,
                    item.name,
                    item._id,
                    '要更换的id'
                  );
                  ele.workshop = element.mill.length > 0 ? element.mill[1] : '';
                  ele.workspaces = element.workspace[1];
                  ele.workstation = item._id;
                }
              });
            }
          }
        } // 匹配对应公司的车间岗位  存储id
      }
      planData.push(ele);
    }
    if (oldPlan) {
      // 更新旧计划
      await ctx.service.db.updateOne(
        'ProtectionPlan',
        { EnterpriseID },
        { plan: planData }
      );
    } else {
      // 创建计划
      await ctx.service.db.create('ProtectionPlan', {
        EnterpriseID,
        plan: planData,
      });
    }
  }

  async getProtectionPlan(data, grantType, requestData) {
    const { ctx } = this;
    try {
      const EnterpriseID = ctx.session.adminUserInfo
        ? ctx.session.adminUserInfo.EnterpriseID
        : '';
      const enterpriseIds = await ctx.helper.getScopeData('enterprise_ids');

      // 获取分页参数
      const currentPage = parseInt(
        requestData && requestData.page ? requestData.page : 1
      );
      const pageSize = parseInt(
        requestData && requestData.limit ? requestData.limit : 10
      );
      const skip = (currentPage - 1) * pageSize;
      const limit = pageSize;

      // 根据授权类型分开处理查询
      if (grantType === 'depart') {
        // 部门类型直接返回查询结果
        return await this.getProtectionPlanForDepart(
          data,
          enterpriseIds,
          currentPage,
          pageSize,
          skip,
          limit
        );
      } else if (grantType === 'mill') {
        // 车间岗位类型需要额外处理
        return await this.getProtectionPlanForMill(
          data,
          enterpriseIds,
          currentPage,
          pageSize,
          skip,
          limit,
          EnterpriseID
        );
      }

      // 默认返回空数据
      return {
        list: [],
        total: 0,
        currentPage,
        pageSize,
      };
    } catch (error) {
      ctx.logger.error('获取防护用品计划失败:', error);
      throw error;
    }
  }

  // 处理部门类型的防护用品计划查询
  async getProtectionPlanForDepart(
    data,
    enterpriseIds,
    currentPage,
    pageSize,
    skip,
    limit
  ) {
    const { ctx } = this;
    const query = {
      EnterpriseID: { $in: enterpriseIds },
      grantType: 'depart',
    };
    let total = 0;
    let plan = null;

    // 判断是否需要根据特定数据过滤
    if (Array.isArray(data) && data.length > 0) {
      // 使用投影优化查询性能
      const pipeline = [
        {
          $match: { EnterpriseID: { $in: enterpriseIds }, grantType: 'depart' },
        },
        {
          $addFields: {
            filteredElements: {
              $filter: {
                input: {
                  $map: {
                    input: '$subRegion',
                    as: 'subRegionItem',
                    in: { $arrayElemAt: [ '$$subRegionItem', -1 ] },
                  },
                },
                as: 'lastElement',
                cond: { $in: [ '$$lastElement', data ] },
              },
            },
          },
        },
        { $match: { 'filteredElements.0': { $exists: true } } },
        {
          $project: {
            _id: 1,
            EnterpriseID: 1,
            departId: 1,
            departName: 1,
            products: 1,
            planStatus: 1,
            grantType: 1,
            subRegion: 1,
            category: 1,
          },
        },
      ];

      // 计数
      const countPipeline = [ ...pipeline ];
      countPipeline.push({ $count: 'total' });
      const countResult = await ctx.service.db.aggregate(
        'ProtectionPlan',
        countPipeline
      );
      total = countResult.length > 0 ? countResult[0].total : 0;

      // 分页
      pipeline.push({ $skip: skip }, { $limit: limit });
      plan = await ctx.service.db.aggregate('ProtectionPlan', pipeline);
    } else {
      // 无特定过滤时直接查询
      // 获取总数
      total = await ctx.service.db.count('ProtectionPlan', query);

      // 添加投影以减少返回数据量
      const projection = {
        _id: 1,
        EnterpriseID: 1,
        departId: 1,
        departName: 1,
        products: 1,
        planStatus: 1,
        grantType: 1,
        subRegion: 1,
        category: 1,
      };

      plan = await ctx.service.db.find('ProtectionPlan', query, projection, {
        skip,
        limit,
        sort: { _id: -1 },
      });
    }

    // 处理部门名称
    const result = [];
    if (Array.isArray(plan) && plan.length > 0) {
      for (let i = 0; i < plan.length; i++) {
        const item = plan[i];
        if (!item.departName && item.departId) {
          const departDoc = await ctx.service.db.findOne(
            'Dingtree',
            { _id: item.departId },
            { name: 1 }
          );
          if (departDoc) {
            item.departName = departDoc.name;
          }
        }
        result.push(item);
      }
    }

    return {
      list: result,
      total,
      currentPage,
      pageSize,
    };
  }

  // 处理车间类型的防护用品计划查询 - 使用聚合查询优化
  async getProtectionPlanForMill(
    data,
    enterpriseIds,
    currentPage,
    pageSize,
    skip,
    limit
    // EnterpriseID
  ) {
    const { ctx } = this;

    // 获取用户权限
    const userid = ctx.session.adminUserInfo._id;
    const millConstructionIds = await ctx.helper.getScopeData(
      'millConstruction_ids',
      userid
    );

    // 构建聚合管道
    const pipeline = [
      // 基础匹配
      {
        $match: {
          EnterpriseID: { $in: enterpriseIds },
          grantType: 'mill',
        },
      },
    ];

    // 添加特定工种过滤
    if (data && Array.isArray(data) && data.length > 0) {
      pipeline.push({
        $match: {
          nodeFullId: { $in: data },
        },
      });
    }

    // 添加权限过滤
    if (millConstructionIds && millConstructionIds.length > 0) {
      const hierarchyRegex = this.buildHierarchyRegex(millConstructionIds);
      pipeline.push({
        $match: {
          nodeFullId: { $regex: hierarchyRegex },
        },
      });
    }

    // 联查节点信息
    pipeline.push(
      {
        $lookup: {
          from: 'flatMillConstructionMaterialized',
          localField: 'nodeFullId',
          foreignField: 'fullId',
          as: 'nodeInfo',
        },
      },
      // 解构节点信息
      {
        $unwind: '$nodeInfo',
      },
      // 联查父级车间信息（针对岗位）
      {
        $lookup: {
          from: 'flatMillConstructionMaterialized',
          let: { parentId: '$nodeInfo.parentId' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: [ '$_id', '$$parentId' ] },
                    { $eq: [ '$level', 'workspaces' ] },
                  ],
                },
              },
            },
          ],
          as: 'parentInfo',
        },
      },
      // 添加显示字段
      {
        $addFields: {
          workspaceName: {
            $cond: {
              if: { $eq: [ '$nodeInfo.level', 'workspaces' ] },
              then: '$nodeInfo.name',
              else: { $arrayElemAt: [ '$parentInfo.name', 0 ] },
            },
          },
          stationName: {
            $cond: {
              if: { $eq: [ '$nodeInfo.level', 'stations' ] },
              then: '$nodeInfo.name',
              else: null,
            },
          },
        },
      }
    );

    // 获取总数（用于分页）
    const countPipeline = [ ...pipeline, { $count: 'total' }];
    const countResult = await ctx.model.ProtectionPlan.aggregate(countPipeline);
    const total = countResult.length > 0 ? countResult[0].total : 0;

    // 添加分页和清理字段
    pipeline.push(
      { $skip: skip },
      { $limit: limit },
      {
        $project: {
          nodeInfo: 0,
          parentInfo: 0,
        },
      }
    );

    // 执行聚合查询
    const result = await ctx.model.ProtectionPlan.aggregate(pipeline);

    return {
      list: result,
      total,
      currentPage,
      pageSize,
    };
  }

  // 构建层级正则表达式的辅助方法
  buildHierarchyRegex(millConstructionIds) {
    if (
      !Array.isArray(millConstructionIds) ||
      millConstructionIds.length === 0
    ) {
      return /^$/; // 匹配空字符串，即不匹配任何内容
    }

    // 为每个 millConstructionId 构建层级匹配模式
    const patterns = millConstructionIds.map(id => {
      // 转义特殊字符
      const escapedId = id.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      // 匹配以该ID开头的所有层级（包括自身和所有子级）
      return `^${escapedId}(/.*)?$`;
    });

    // 使用 OR 操作符组合所有模式
    return new RegExp(patterns.join('|'));
  }

  // 旧版本的车间类型查询方法（保留作为备用）
  async getProtectionPlanForMillOld(
    data,
    enterpriseIds,
    currentPage,
    pageSize,
    skip,
    limit,
    EnterpriseID
  ) {
    const { ctx } = this;
    const query = {
      EnterpriseID: { $in: enterpriseIds },
      grantType: 'mill',
    };
    let total = 0;
    let plan = null;

    if (Array.isArray(data) && data.length > 0) {
      // 有选择车间岗位时的查询方式
      const pipeLine = [
        { $match: { EnterpriseID: { $in: enterpriseIds }, grantType: 'mill' } },
        {
          $match: {
            $or: [
              { workstation: { $in: data } },
              { workshop: { $in: data } },
              { workspaces: { $in: data } },
            ],
          },
        },
      ];

      // 计数
      const countPipeline = [ ...pipeLine ];
      countPipeline.push({ $count: 'total' });
      const countResult = await ctx.service.db.aggregate(
        'ProtectionPlan',
        countPipeline
      );
      total = countResult.length > 0 ? countResult[0].total : 0;

      // 分页
      pipeLine.push({ $skip: skip }, { $limit: limit });
      plan = await ctx.service.db.aggregate('ProtectionPlan', pipeLine);
    } else {
      // 无选择车间岗位时的查询方式
      total = await ctx.service.db.count('ProtectionPlan', query);
      plan = await ctx.service.db.find('ProtectionPlan', query, null, {
        skip,
        limit,
        sort: { _id: -1 },
      });
    }

    // 这里匹配一下车间岗位的名称返回前端
    const result = [];
    if (Array.isArray(plan) && plan.length > 0) {
      // 批量收集所有需要查询的车间、岗位ID
      const workshopIds = [];
      const workspacesIds = [];
      const workstationIds = [];

      for (let j = 0; j < plan.length; j++) {
        if (plan[j]) {
          if (plan[j].workshop) workshopIds.push(plan[j].workshop);
          if (plan[j].workspaces) workspacesIds.push(plan[j].workspaces);
          if (plan[j].workstation) workstationIds.push(plan[j].workstation);
        }
      }

      // 批量查询车间信息
      const workshopMap = {};
      if (workshopIds.length > 0) {
        const workshopDocs = await ctx.service.db.find(
          'MillConstruction',
          { _id: { $in: workshopIds } },
          { name: 1, harmFactors: 1 }
        );
        workshopDocs.forEach(doc => {
          workshopMap[doc._id] = {
            name: doc.name,
            harmFactors: doc.harmFactors
              ? doc.harmFactors.map(e => e[1])
              : [],
          };
        });
      }
      // 处理计划数据
      for (let j = 0; j < plan.length; j++) {
        if (plan[j]) {
          const obj = JSON.parse(JSON.stringify(plan[j]));
          obj.harmFactors = [];
          obj.workspacesId = obj.workspaces;
          obj.workstationId = obj.workstation;
          obj.workshopId = obj.workshop;
          obj.employeeId = obj.employee;

          const { workshopInfo, workspacesInfo, workstationInfo } =
            await this.findMill({
              workshop: obj.workshopId,
              workspaces: obj.workspacesId,
              workstation: obj.workstationId,
            });
          if (workshopInfo) {
            obj.workshop = workshopInfo.name;
            obj.harmFactors.push(...workshopInfo.harmFactors);
          }
          if (workspacesInfo) {
            obj.workspaces = workspacesInfo.name
              ? workspacesInfo.name
              : workspacesInfo.workspaces;
            obj.harmFactors.push(...workspacesInfo.harmFactors);
          }
          if (workstationInfo) {
            obj.workstation = workstationInfo.name
              ? workstationInfo.name
              : workstationInfo.workstations;
            workstationInfo.harmFactors
              ? obj.harmFactors.push(...workstationInfo.harmFactors)
              : obj.harmFactors.push(
                ...workstationInfo.workstations[0].harmFactors.map(
                  e => e[1]
                )
              );
          }

          obj.harmFactors = Array.from(new Set(obj.harmFactors));

          if (obj.employee) {
            const res = await ctx.service.db.findOne(
              'Employee',
              {
                _id: obj.employee,
              },
              { name: 1 }
            );
            if (res) {
              obj.employee = res.name;
            }
          }

          if (obj.departId) {
            const departDoc = await ctx.service.db.findOne(
              'Dingtree',
              { _id: obj.departId },
              { name: 1 }
            );
            if (departDoc) {
              obj.departName = departDoc.name;
            }
          }
          result.push(obj);
        }
      }
      return {
        list: result,
        total,
        currentPage,
        pageSize,
      };
    } else if (plan && plan.plan) {
      for (let i = 0; i < plan.plan.length; i++) {
        const obj = JSON.parse(JSON.stringify(plan.plan[i]));
        obj.harmFactors = [];
        obj.workspacesId = obj.workspaces;
        obj.workstationId = obj.workstation;
        obj.workshopId = obj.workshop;
        obj.employeeId = obj.employee;

        const { workshopInfo, workspacesInfo, workstationInfo } =
          await this.findMill({
            workshop: obj.workshopId,
            workspaces: obj.workspacesId,
            workstation: obj.workstationId,
            EnterpriseID,
          });
        if (workshopInfo) {
          obj.workshop = workshopInfo.name;
          obj.harmFactors.push(...workshopInfo.harmFactors);
        }
        if (workspacesInfo) {
          obj.workspaces = workspacesInfo.name
            ? workspacesInfo.name
            : workspacesInfo.workspaces;
          obj.harmFactors.push(...workspacesInfo.harmFactors);
        }
        if (workstationInfo) {
          obj.workstation = workstationInfo.name
            ? workstationInfo.name
            : workstationInfo.workstations;
          workstationInfo.harmFactors
            ? obj.harmFactors.push(...workstationInfo.harmFactors)
            : obj.harmFactors.push(
              ...workstationInfo.workstations[0].harmFactors.map(e => e[1])
            );
        }

        obj.harmFactors = Array.from(new Set(obj.harmFactors));

        if (obj.employee) {
          const res = await ctx.service.db.findOne(
            'Employee',
            { _id: obj.employee },
            { name: 1 }
          );
          if (res) {
            obj.employee = res.name;
          }
        }
        plan.plan[i] = obj;
      }
      return {
        list: plan.plan,
        total: plan.plan.length,
        currentPage: 1,
        pageSize: plan.plan.length,
      };
    }

    // 无数据返回空数组
    return {
      list: [],
      total,
      currentPage,
      pageSize,
    };
  }

  // 优化findMill方法，增加缓存
  async findMill({ workshop = '', workspaces = '', workstation = '' }) {
    // console.log(33333, workshop, workspaces);
    const { ctx } = this;
    let workshopInfo = null;
    let workspacesInfo = null;
    let workstationInfo = null;
    // console.log(workshop, workspaces, workstation, 'workshopInfo');
    if (workshop) {
      const matchParams = [
        { $match: { _id: workshop } },
        { $project: { name: 1, harmFactors: 1, children: 1 } },
      ];
      if (workspaces) {
        matchParams.push(
          ...[
            {
              $unwind: {
                path: '$children',
                preserveNullAndEmptyArrays: true,
              },
            },
            { $match: { 'children._id': workspaces } },
          ]
        );
      }
      if (workstation) {
        matchParams.push(
          ...[
            {
              $unwind: {
                path: '$children.children',
                preserveNullAndEmptyArrays: true,
              },
            },
            { $match: { 'children.children._id': workstation } },
          ]
        );
      }
      let mill = await ctx.service.db.aggregate(
        'MillConstruction',
        matchParams
      );
      mill = mill[0];
      if (mill) {
        // console.log(mill, 'mill');
        const harmFactors = Array.isArray(mill.children)
          ? mill.children[0].harmFactors
          : mill.children.harmFactors;
        if (workshop) {
          workshopInfo = {
            type: 'workshop',
            name: mill.name,
            _id: mill._id,
            harmFactors: mill.harmFactors
              ? mill.harmFactors.map(e => e[1])
              : [],
          };
        }
        if (workspaces) {
          workspacesInfo = {
            type: 'workspaces',
            name: mill.children.name,
            _id: mill.children._id,
            harmFactors: harmFactors ? harmFactors.map(e => e[1]) : [],
          };
        } else {
          workspacesInfo = {
            type: 'workspaces',
            workspaces: mill.children.map(e => {
              return { name: e.name, _id: e._id };
            }),
            harmFactors: harmFactors ? harmFactors.map(e => e[1]) : [],
          };
        }
        if (workspaces && workstation) {
          workstationInfo = {
            type: 'workstation',
            name: mill.children.children.name,
            _id: mill.children.children._id,
            harmFactors: mill.children.children.harmFactors
              ? mill.children.children.harmFactors.map(e => e[1])
              : [],
          };
        } else if (workspaces && !workstation) {
          workstationInfo = {
            type: 'workstation',
            workstations: mill.children.children.map(e => {
              return { name: e.name, _id: e._id, harmFactors: e.harmFactors };
            }),
            // harmFactors: harmFactors ? harmFactors.map(e => e[1]) : [],
          };
        } else if (!workspaces && !workstation) {
          const arr = mill.children.map(item => item.children);
          workstationInfo = {
            type: 'workstation',
            workstations: [].concat(...arr).map(e => {
              return { name: e.name, _id: e._id, harmFactors: e.harmFactors };
            }),
            // harmFactors: harmFactors ? harmFactors.map(e => e[1]) : [],
          };
        }
      }
    } else {
      const matchParams = [
        { $match: { _id: workspaces } },
        { $project: { name: 1, harmFactors: 1, children: 1 } },
      ];

      if (workstation) {
        matchParams.push(
          ...[
            {
              $unwind: {
                path: '$children',
                preserveNullAndEmptyArrays: true,
              },
            },
            { $match: { 'children._id': workstation } },
          ]
        );
      }
      let mill = await ctx.service.db.aggregate(
        'MillConstruction',
        matchParams
      );
      mill = mill[0];
      if (mill) {
        const harmFactors = Array.isArray(mill.children)
          ? mill.children[0].harmFactors
          : mill.children.harmFactors;
        if (workspaces) {
          workspacesInfo = {
            type: 'workspaces',
            name: mill.name,
            _id: mill._id,
            harmFactors: mill.harmFactors
              ? mill.harmFactors.map(e => e[1])
              : [],
          };
        }
        if (workstation) {
          workstationInfo = {
            type: 'workstation',
            name: mill.children.name,
            _id: mill.children._id,
            harmFactors: harmFactors ? harmFactors.map(e => e[1]) : [],
          };
        } else {
          workstationInfo = {
            type: 'workstation',
            workstations: mill.children.map(e => {
              return { name: e.name, _id: e._id };
            }),
            harmFactors: harmFactors ? harmFactors.map(e => e[1]) : [],
          };
        }
      }
    }

    return {
      workshopInfo,
      workspacesInfo,
      workstationInfo,
    };
  }

  async saveOneprotection(data) {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo
      ? ctx.session.adminUserInfo.EnterpriseID
      : '';

    console.log('=== saveOneprotection 调试信息开始 ===');
    console.log('原始数据:', JSON.stringify(data, null, 2));
    console.log('企业ID:', EnterpriseID);
    console.log('categoryPath类型:', typeof data.categoryPath);
    console.log('categoryPath是否为数组:', Array.isArray(data.categoryPath));
    console.log('categoryPath值:', data.categoryPath);

    // 处理数据类型转换
    data.surplus = parseInt(data.surplus);
    console.log('转换后的surplus:', data.surplus);

    // 处理分类路径数据：如果categoryPath是数组，则存储到categoryPathIds字段
    if (Array.isArray(data.categoryPath)) {
      console.log('检测到数组格式的categoryPath，开始处理...');
      data.categoryPathIds = data.categoryPath;
      console.log('设置categoryPathIds:', data.categoryPathIds);

      // 根据ID数组生成路径字符串
      try {
        const pathString = await this.generatePathFromIds(data.categoryPath);
        console.log('生成的路径字符串:', pathString);
        data.categoryPath = pathString;
      } catch (error) {
        console.log('生成分类路径字符串失败:', error.message);
        delete data.categoryPath; // 如果生成失败，删除该字段
      }
    }

    console.log('处理后的数据:', JSON.stringify(data, null, 2));

    // 判断是否是新增
    if (!data._id) {
      console.log('执行新增操作...');
      console.log('查询条件 - EnterpriseID:', EnterpriseID);
      console.log('更新操作 - 分类名称:', data.name);

      // 获取仓库ID
      let warehouseId = data.warehouseId;
      if (!warehouseId) {
        const publicWarehouse = await ctx.model.Warehouse.findOne({
          EnterpriseID,
          isPublic: true,
        });
        if (!publicWarehouse) {
          throw new Error('未找到公共仓库，请联系管理员初始化仓库');
        }
        warehouseId = publicWarehouse._id;
      }

      // 检查目标分类是否存在
      const existingList = await ctx.service.db.findOne('ProtectiveSuppliesList', {
        EnterpriseID,
        warehouseId,
      });
      console.log('现有清单结构:', JSON.stringify(existingList, null, 2));

      if (!existingList) {
        console.log('清单不存在，需要先创建清单');
        // 如果清单不存在，先创建一个基础清单
        const newList = {
          EnterpriseID,
          warehouseId,
          list: [{
            name: data.categoryName || '安全帽', // 使用分类名称作为列表名称
            tableHeader: [
              'materialCode',
              'product',
              'productSpec',
              'modelNumber',
              'style',
              'protectionType',
              'useMethod',
              'industryEnvironment',
              'harmFactors',
              'surplus',
            ],
            data: [ data ],
          }],
        };

        const createResult = await ctx.service.db.create('ProtectiveSuppliesList', newList);
        console.log('创建新清单结果:', createResult);
        console.log('=== saveOneprotection 调试信息结束 ===');
        return '新增成功';
      }

      // 检查是否有匹配的分类
      const targetCategory = existingList.list.find(item => item.name === data.name);
      console.log('目标分类:', targetCategory);

      if (!targetCategory) {
        console.log('目标分类不存在，需要先创建分类或使用现有分类');
        // 如果没有匹配的分类，使用第一个分类或创建新分类
        const categoryName = data.categoryName || '安全帽';
        const existingCategory = existingList.list.find(item => item.name === categoryName);

        if (existingCategory) {
          console.log('使用现有分类:', categoryName);
          data.name = categoryName;
        } else {
          console.log('创建新分类:', categoryName);
          // 添加新分类
          const newCategory = {
            name: categoryName,
            tableHeader: [
              'materialCode',
              'product',
              'productSpec',
              'modelNumber',
              'style',
              'protectionType',
              'useMethod',
              'industryEnvironment',
              'harmFactors',
              'surplus',
            ],
            data: [],
          };

          await ctx.service.db.updateOne(
            'ProtectiveSuppliesList',
            { EnterpriseID, warehouseId: data.warehouseId || 'public' },
            { $push: { list: newCategory } }
          );
          data.name = categoryName;
        }
      }

      try {
        const result = await ctx.service.db.updateOne(
          'ProtectiveSuppliesList',
          { EnterpriseID, warehouseId },
          { $push: { 'list.$[t].data': data } },
          { arrayFilters: [{ 't.name': data.name }] }
        );
        console.log('新增操作结果:', result);
        console.log('=== saveOneprotection 调试信息结束 ===');
        return '新增成功';
      } catch (error) {
        console.log('新增操作失败:', error);
        console.log('=== saveOneprotection 调试信息结束 ===');
        throw error;
      }
    }

    console.log('执行更新操作...');
    try {
      const result = await ctx.service.db.updateOne(
        'ProtectiveSuppliesList',
        { EnterpriseID },
        { $set: { 'list.$[t].data.$[i]': data } },
        { arrayFilters: [{ 't.name': data.name }, { 'i._id': data._id }] }
      );
      console.log('更新操作结果:', result);
      console.log('=== saveOneprotection 调试信息结束 ===');
      return '保存成功';
    } catch (error) {
      console.log('更新操作失败:', error);
      console.log('=== saveOneprotection 调试信息结束 ===');
      throw error;
    }
  }

  /**
   * 根据分类ID数组生成路径字符串
   * @param {Array} categoryIds - 分类ID数组
   * @returns {String} 路径字符串，如 "大类/小类/细分类"
   */
  async generatePathFromIds(categoryIds) {
    const { ctx } = this;

    console.log('=== generatePathFromIds 调试信息 ===');
    console.log('输入的categoryIds:', categoryIds);
    console.log('categoryIds类型:', typeof categoryIds);
    console.log('categoryIds是否为数组:', Array.isArray(categoryIds));

    if (!Array.isArray(categoryIds) || categoryIds.length === 0) {
      console.log('categoryIds为空或不是数组，返回空字符串');
      return '';
    }

    try {
      // 获取最后一个分类ID对应的分类信息
      const lastCategoryId = categoryIds[categoryIds.length - 1];
      console.log('最后一个分类ID:', lastCategoryId);

      const category = await ctx.service.db.findOne('ProtectionCategory', {
        _id: lastCategoryId,
      });
      console.log('查询到的分类信息:', category);

      if (category && category.path) {
        console.log('使用分类的path字段:', category.path);
        return category.path;
      }

      // 如果没有path字段，则逐个查询分类名称并拼接
      console.log('没有path字段，开始逐个查询分类名称...');
      const categoryNames = [];
      for (const categoryId of categoryIds) {
        console.log('查询分类ID:', categoryId);
        const cat = await ctx.service.db.findOne('ProtectionCategory', {
          _id: categoryId,
        });
        console.log('查询结果:', cat);
        if (cat && cat.name) {
          categoryNames.push(cat.name);
          console.log('添加分类名称:', cat.name);
        }
      }

      const result = categoryNames.join('/');
      console.log('最终生成的路径:', result);
      console.log('=== generatePathFromIds 调试信息结束 ===');
      return result;
    } catch (error) {
      console.log('生成分类路径失败:', error.message);
      console.log('错误详情:', error);
      console.log('=== generatePathFromIds 调试信息结束 ===');
      return '';
    }
  }

  async savePageInfo(params) {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo
      ? ctx.session.adminUserInfo.EnterpriseID
      : '';
    const name = params.name;
    const column = JSON.parse(params.column);
    // const oldName = await ctx.model.ProtectionPlan.findOne({ EnterpriseID, 'list.name': name });
    const oldName = await ctx.service.db.findOne('ProtectionPlan', {
      EnterpriseID,
      'list.name': name,
    });
    if (params.id) {
      console.log('有id的需要保存');
      await ctx.service.db.updateOne(
        'ProtectiveSuppliesList',
        { EnterpriseID },
        { $set: { 'list.$[t].name': name, 'list.$[t].tableHeader': column } },
        { arrayFilters: [{ 't._id': params.id }] }
      );
    } else {
      console.log('000000');
      if (oldName) {
        // 已有此分类
        return 0;
      }
      await ctx.service.db.updateOne(
        'ProtectiveSuppliesList',
        { EnterpriseID },
        { $push: { list: { name, tableHeader: column, data: [] } } }
      );
      return '保存成功';
    }
  }
  async saveSingle(data) {
    const { ctx } = this;

    // 处理新的 fullId 格式数据
    if (data.nodeFullId) {
      // 从 fullId 解析出节点信息
      const nodeInfo = await ctx.model.FlatMillConstructionMaterialized.findOne(
        {
          fullId: data.nodeFullId,
        }
      ).select('name level parentId');

      if (nodeInfo) {
        data.nodeLevel = nodeInfo.level;
        data.nodeName = nodeInfo.name;
      }
    }
    console.log(data,'data');

    // 修复Bug：智能判断是更新还是新增
    // 如果没有_id但有nodeFullId，先查询是否已存在记录
    if (!data._id && data.nodeFullId) {
      const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
      const existingPlan = await ctx.service.db.findOne('ProtectionPlan', {
        nodeFullId: data.nodeFullId,
        EnterpriseID,
      });

      if (existingPlan) {
        data._id = existingPlan._id;
        ctx.logger.info(`[saveSingle] 通过nodeFullId找到已存在的记录，ID: ${existingPlan._id}`);
      } else {
        ctx.logger.info(`[saveSingle] 通过nodeFullId未找到已存在的记录，将创建新记录`);
      }
    }

    if (data._id) {
      // 修复Bug：更智能的状态处理逻辑
      // 1. 如果用户明确设置为"无需配置"，则保持该状态
      // 2. 如果有防护用品配置，则设为"已配置"
      // 3. 如果没有防护用品配置且不是"无需配置"，则设为"未配置"
      let finalConfigStatus;
      if (data.configStatus === 'no_need') {
        finalConfigStatus = 'no_need';
      } else if (data.products && data.products.length > 0) {
        finalConfigStatus = 'configured';
      } else {
        finalConfigStatus = 'unconfigured';
      }

      ctx.logger.info(`[saveSingle] 更新配发标准 ${data._id}:`);
      ctx.logger.info(`  - 传入状态: ${data.configStatus}`);
      ctx.logger.info(`  - 防护用品数量: ${data.products ? data.products.length : 0}`);
      ctx.logger.info(`  - 最终状态: ${finalConfigStatus}`);

      const updateData = {
        products: data.products,
        subRegion: data.subRegion,
        category: data.category,
        configStatus: finalConfigStatus,
      };

      // 添加新字段
      if (data.nodeFullId) {
        updateData.nodeFullId = data.nodeFullId;
        updateData.nodeLevel = data.nodeLevel;
        updateData.nodeName = data.nodeName;
      }

      await ctx.service.db.updateOne(
        'ProtectionPlan',
        { _id: data._id },
        { $set: updateData }
      );

      // 更新配发标准后重新生成领用记录 - 使用新的通用方法
      try {
        await ctx.service.receiveRecord.generateRecordsForPlan(data._id);
        ctx.logger.info(`[saveSingle] 更新配发标准 ${data._id} 后生成记录完成`);
      } catch (error) {
        ctx.logger.error(`[saveSingle] 更新配发标准 ${data._id} 后生成记录失败:`, error);
      }

      try {
        // 更新领用记录
        // 如果计划更新时，删了防护用品，那么就把该计划未领用的该防护用品记录删除
        const currentProducts = data.products.map(e => e.product);
        const pipeLine = [
          {
            $match: {
              planId: data._id,
              sign: { $exists: false },
              'products.0.product': { $nin: currentProducts },
            },
          },
        ];
        const needRemoveDocs = await ctx.service.db.aggregate(
          'ReceiveRecord',
          pipeLine
        );
        const needRemoveDocsIds = needRemoveDocs.map(e => e._id);
        await ctx.service.db.remove('ReceiveRecord', {
          _id: { $in: needRemoveDocsIds },
        });
      } catch (error) {
        console.log(error);
      }

      try {
        // 对比删除多余的领用记录
        // 判断是否有删除子区域，如果有的话就把对应的未领用记录删除
        let flatSubRegion = data.subRegion.flat();
        flatSubRegion = Array.from(new Set(flatSubRegion));

        const matchQuery = {
          sign: { $exists: false },
          planId: data._id,
        };
        if (data.grantType === 'mill') {
          matchQuery.workstation = { $nin: flatSubRegion };
        }

        const receiveRecords = await ctx.service.db.aggregate('ReceiveRecord', [
          {
            $match: matchQuery,
          },
        ]);

        for (let i = 0; i < receiveRecords.length; i++) {
          const item = receiveRecords[i];
          await ctx.service.db.remove('ReceiveRecord', { _id: item._id });
        }
      } catch (error) {
        console.log(error);
      }
    } else {
      // 新增配发标准
      const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';

      // 修复Bug：新增时也使用相同的智能状态处理逻辑
      let finalConfigStatus;
      if (data.configStatus === 'no_need') {
        finalConfigStatus = 'no_need';
      } else if (data.products && data.products.length > 0) {
        finalConfigStatus = 'configured';
      } else {
        finalConfigStatus = 'unconfigured';
      }

      ctx.logger.info(`[saveSingle] 新增配发标准:`);
      ctx.logger.info(`  - 传入状态: ${data.configStatus}`);
      ctx.logger.info(`  - 防护用品数量: ${data.products ? data.products.length : 0}`);
      ctx.logger.info(`  - 最终状态: ${finalConfigStatus}`);

      const newPlan = {
        EnterpriseID,
        products: data.products,
        subRegion: data.subRegion,
        category: data.category,
        configStatus: finalConfigStatus,
      };

      // 添加新字段
      if (data.nodeFullId) {
        newPlan.nodeFullId = data.nodeFullId;
        newPlan.nodeLevel = data.nodeLevel;
        newPlan.nodeName = data.nodeName;
      }

      const doc = await ctx.service.db.create('ProtectionPlan', newPlan);

      // 新建配发标准后生成领用记录 - 使用新的通用方法
      try {
        await ctx.service.receiveRecord.generateRecordsForPlan(doc._id);
        ctx.logger.info(`[saveSingle] 新建配发标准 ${doc._id} 后生成记录完成`);
      } catch (error) {
        ctx.logger.error(`[saveSingle] 新建配发标准 ${doc._id} 后生成记录失败:`, error);
      }
    }
    return '保存成功';
  }

  async autogenerationProtective(params) {
    const { ctx } = this;
    // const plan = await ctx.model.ProtectionPlan.findOne({ EnterpriseID: params });
    const plan = await ctx.service.db.findOne('ProtectionPlan', {
      EnterpriseID: params,
    });
    return plan;
  }

  /**
   * 新增配发标准：给选中的父级区域下的所有岗位配备相同的标准
   * @param {Object} data - 包含 formData, parentMills, childMills 的数据
   * @param {String} grantType - 配发类型
   * @param {String} EnterpriseID - 企业ID
   */
  async createStandardsForRegions(data, grantType, EnterpriseID) {
    const { ctx } = this;

    try {
      const { formData, parentMills, childMills } = data;
      const results = {
        success: 0,
        failed: 0,
        errors: [],
        created: [],
      };

      // 获取选中区域下的所有岗位
      const stationList = await this.getStationsForRegions(parentMills, childMills, EnterpriseID);

      if (stationList.length === 0) {
        throw new Error('未找到符合条件的岗位');
      }

      // 为每个岗位创建配发标准
      for (const station of stationList) {
        try {
          // 检查是否已存在配发标准
          const existingPlan = await ctx.service.db.findOne('ProtectionPlan', {
            EnterpriseID,
            nodeFullId: station.fullId,
            planStatus: { $ne: 0 }, // 排除已删除的
          });

          if (existingPlan) {
            // 更新现有配发标准
            await ctx.service.db.updateOne(
              'ProtectionPlan',
              { _id: existingPlan._id },
              {
                $set: {
                  products: formData.products,
                  category: formData.category || '',
                  configStatus: 'configured',
                  updateTime: new Date(),
                },
              }
            );

            // 重新生成领用记录
            await ctx.service.receiveRecord.generateRecordsForPlan(existingPlan._id);

            results.success++;
            results.created.push({
              stationName: station.stationName || station.name,
              action: 'updated',
            });
          } else {
            // 创建新的配发标准
            const newPlan = {
              EnterpriseID,
              nodeFullId: station.fullId,
              nodeLevel: 'stations',
              nodeName: station.stationName || station.name,
              products: formData.products,
              category: formData.category || '',
              configStatus: 'configured',
              grantType,
              createTime: new Date(),
            };

            const doc = await ctx.service.db.create('ProtectionPlan', newPlan);

            // 生成领用记录
            await ctx.service.receiveRecord.generateRecordsForPlan(doc._id);

            results.success++;
            results.created.push({
              stationName: station.stationName || station.name,
              action: 'created',
            });
          }
        } catch (error) {
          ctx.logger.error(`为岗位 ${station.stationName || station.name} 创建配发标准失败:`, error);
          results.failed++;
          results.errors.push({
            stationName: station.stationName || station.name,
            error: error.message,
          });
        }
      }

      return results;
    } catch (error) {
      ctx.logger.error('批量创建配发标准失败:', error);
      throw error;
    }
  }

  /**
   * 根据选中的父级区域和子区域获取所有岗位
   * @param {Array} parentMills - 选中的父级区域
   * @param {Array} childMills - 选中的子区域
   * @param {String} EnterpriseID - 企业ID
   */
  async getStationsForRegions(parentMills, childMills, EnterpriseID) {
    const { ctx } = this;

    try {
      const query = {
        EnterpriseID,
        level: 'stations', // 只获取岗位级别的数据
      };

      // 如果选择了子区域，则只获取子区域下的岗位
      if (childMills && childMills.length > 0) {
        // childMills 是二维数组，需要扁平化处理
        const childIds = childMills.flat();
        query.parentId = { $in: childIds };
        ctx.logger.info(`使用子区域查询，子区域IDs: ${JSON.stringify(childIds)}`);
      } else if (parentMills && parentMills.length > 0) {
        // 如果只选择了父级区域，获取父级区域下的所有岗位
        const parentIds = parentMills.map(parent => parent.id || parent._id);

        // 需要递归查找父级区域下的所有岗位
        // 这里使用正则表达式匹配 fullId 以父级ID开头的所有岗位
        const parentFullIds = parentMills.map(parent => parent.id || parent._id);
        const regexPattern = parentFullIds.map(id => `^${id}/`).join('|');
        query.fullId = { $regex: regexPattern };
        ctx.logger.info(`使用父级区域查询，父级IDs: ${JSON.stringify(parentIds)}, 正则: ${regexPattern}`);
      }

      ctx.logger.info(`查询条件: ${JSON.stringify(query)}`);

      // 先查看数据库中有什么数据
      const allStations = await ctx.service.db.find('FlatMillConstructionMaterialized', {
        EnterpriseID,
        level: 'stations'
      });
      ctx.logger.info(`数据库中总共有 ${allStations.length} 个岗位`);
      if (allStations.length > 0) {
        ctx.logger.info(`示例岗位数据: ${JSON.stringify(allStations[0])}`);
      }

      const stations = await ctx.service.db.find('FlatMillConstructionMaterialized', query);

      ctx.logger.info(`找到 ${stations.length} 个岗位需要配置标准`);
      return stations;
    } catch (error) {
      ctx.logger.error('获取岗位列表失败:', error);
      throw error;
    }
  }

  async autogenerationProtectiveOld(params) {
    const { ctx } = this;
    const plan = await ctx.service.db.findOne('ProtectionPlan', {
      EnterpriseID: params,
    });
    const formData = [];
    let data = {};

    if (plan) {
      // 如果有计划
      for (let i = 0; i < plan.plan.length; i++) {
        const obj = JSON.parse(JSON.stringify(plan.plan[i]));
        obj.harmFactors = [];
        obj.workspacesId = '';
        obj.workstationId = '';
        let mill; // 车间岗位信息
        if (obj.workshop) {
          const pipeline = [
            { $match: { EnterpriseID: params, _id: obj.workshop } },
            { $unwind: '$children' },
            { $unwind: '$children.children' },
            {
              $match: {
                'children._id': obj.workspaces,
                'children.children._id': obj.workstation,
              },
            },
          ];
          mill = await ctx.service.db.aggregate('MillConstruction', pipeline);
          if (mill.length > 0) {
            for (let j = 0; j < mill.length; j++) {
              const item = mill[j];
              // const name = await ctx.model.Employee.findOne({ _id: item.children.children.employees });
              const name = await ctx.service.db.findOne('Employee', {
                _id: item.children.children.employees,
              });
              console.log(name, '这是个啥？？？？');
              const ele = {
                studio: item.children.name, // 车间
                pname: obj.product, // 用品名称
                receiveMan: name.name, // 领用人
                sku: obj.modelNumber, // 型号
                quantity: obj.number, // 数量
                date: new Date(),
              };
              formData.push(ele);
            }
            // obj.workspaces = mill[0].name;
            // obj.workstation = mill[0].children.name;// 岗位 防护用品 姓名 数量
          }
        } else {
          const pipeline = [
            { $match: { EnterpriseID: params, _id: obj.workspaces } },
            { $unwind: '$children' },
            { $match: { 'children._id': obj.workstation } },
            { $unwind: '$children.children' },
            {
              $lookup: {
                from: 'employees',
                localField: 'children.children.employees',
                foreignField: '_id',
                as: 'employees',
              },
            },
          ];
          mill = await ctx.service.db.aggregate('MillConstruction', pipeline, {
            allowDiskUse: true,
          });
          if (mill.length > 0) {
            for (let j = 0; j < mill.length; j++) {
              const item = mill[j];
              // const name = await ctx.model.Employee.findOne({ _id: item.children.children.employees });
              const name = await ctx.service.db.findOne('Employee', {
                _id: item.children.children.employees,
              });
              const ele = {
                studio: item.children.name, // 车间
                pname: obj.product, // 用品名称
                receiveMan: name ? name.name : '-', // 领用人
                sku: obj.modelNumber, // 型号
                quantity: obj.number, // 数量
                date: new Date(),
              };
              formData.push(ele);
            }
          }
        }
      }
    } else {
      const pipeline = [
        {
          $match: {
            EnterpriseID: params,
          },
        },
        {
          $unwind: '$children',
        },
        {
          $unwind: '$children.children',
        },
        {
          $lookup: {
            from: 'employees',
            localField: 'children.children.employees',
            foreignField: '_id',
            as: 'employee',
          },
        },
      ];
      const res = await ctx.service.db.aggregate('MillConstruction', pipeline, {
        allowDiskUse: true,
      });
      // const res = await ctx.service.mill.findMillConstruction(params);
      // console.log(res, '------------------');
      if (res) {
        for (let i = 0; i < res.length; i++) {
          const ele = res[i];
          if (ele.category === 'workspaces') {
            let protectives = [];
            for (let j = 0; j < ele.children.harmFactors.length; j++) {
              const item = ele.children.harmFactors[j];
              // const protective = await ctx.model.OccupationalexposureLimits.findOne({ chineseName: item[1] }, { protectiveEquipment: 1, chineseName: 1 });
              const protective = await ctx.service.db.findOne(
                'OccupationalexposureLimits',
                { chineseName: item[1] },
                { protectiveEquipment: 1, chineseName: 1 }
              );
              if (
                protective &&
                protective.protectiveEquipment &&
                protective.protectiveEquipment.indexOf('-') === -1
              ) {
                protectives.push(protective.protectiveEquipment);
              }
            }
            protectives = Array.from(new Set(_.compact(protectives))); // 每个人需要发的防护用品；
            for (let j = 0; j < protectives.length; j++) {
              const item = protectives[j];
              if (ele.employee) {
                data = {
                  studio: ele.children.name, // 车间
                  pname: item, // 用品名称
                  receiveMan: ele.employee[0].name, // 领用人
                  sku: '', // 型号
                  quantity: '', // 数量
                  date: '',
                };
                formData.push(data);
              }
            }
            // console.log(protectives, '对应的防护用品==============');
          }
          // formData = _.uniqWith(formData, _.isEqual);
        }
      }
    }
    return formData;
  }

  async immediatePPEPlan(EnterpriseID) {
    const { ctx } = this;
    // const item = await ctx.model.ProtectionPlan.findOne({ EnterpriseID });
    const item = await ctx.service.db.findOne('ProtectionPlan', {
      EnterpriseID,
    });
    const formData = [];
    for (let j = 0; j < item.plan.length; j++) {
      const ele = item.plan[j];
      const pipeline = [
        {
          $match: { EnterpriseID: item.EnterpriseID },
        },
        {
          $unwind: '$formData',
        },
        {
          $match: {
            'formData.pname': ele.product,
            'formData.sku': ele.modelNumber,
          },
        },
        { $project: { 'formData.date': 1 } },
        { $sort: { 'formData.date': 1 } },
      ];
      const res = await ctx.service.db.aggregate('Defendproducts', pipeline);
      let lastTime; // 最后一次发放该防护用品的时间
      if (res) {
        if (res.length) lastTime = res[res.length - 1].formData.date;
        if (
          !res.length ||
          lastTime.getTime() < new Date().getTime() - 86400000 * ele.time
        ) {
          if (ele.workshop) {
            const pipeline = [
              {
                $match: { EnterpriseID: item.EnterpriseID, _id: ele.workshop },
              },
              { $unwind: '$children' },
              { $unwind: '$children.children' },
              {
                $match: {
                  'children._id': ele.workspaces,
                  'children.children._id': ele.workstation,
                },
              },
            ];
            const mill = await ctx.service.db.aggregate(
              'MillConstruction',
              pipeline
            );
            if (mill.length > 0) {
              for (let k = 0; k < mill.length; k++) {
                const workStation = mill[k];
                // const name = await ctx.model.Employee.findOne({ _id: workStation.children.children.employees });
                const name = await ctx.service.db.findOne('Employee', {
                  _id: workStation.children.children.employees,
                });
                const data = {
                  studio: workStation.children.name, // 车间
                  pname: ele.product, // 用品名称
                  receiveMan: name.name, // 领用人
                  sku: ele.modelNumber, // 型号
                  quantity: ele.number, // 数量
                  date: new Date(),
                };
                formData.push(data); // 推进库里
              }
              // obj.workspaces = mill[0].name;
              // obj.workstation = mill[0].children.name;// 岗位 防护用品 姓名 数量
            }
          } else {
            const pipeline = [
              {
                $match: {
                  EnterpriseID: item.EnterpriseID,
                  _id: ele.workspaces,
                },
              },
              { $unwind: '$children' },
              { $match: { 'children._id': ele.workstation } },
              { $unwind: '$children.children' },
              {
                $lookup: {
                  from: 'employees',
                  localField: 'children.children.employees',
                  foreignField: '_id',
                  as: 'employees',
                },
              },
            ];
            const mill = await ctx.service.db.aggregate(
              'MillConstruction',
              pipeline,
              { allowDiskUse: true }
            );
            if (mill.length > 0) {
              for (let k = 0; k < mill.length; k++) {
                const workStation = mill[k];
                // const name = await ctx.model.Employee.findOne({ _id: workStation.children.children.employees });
                const name = await ctx.service.db.findOne('Employee', {
                  _id: workStation.children.children.employees,
                });
                const data = {
                  studio: workStation.children.name, // 车间
                  pname: ele.product, // 用品名称
                  receiveMan: name ? name.name : '-', // 领用人
                  sku: ele.modelNumber, // 型号
                  quantity: ele.number, // 数量
                  date: new Date(),
                };
                formData.push(data);
              }
            }
          }
          await ctx.service.db.updateOne(
            'Defendproducts',
            { EnterpriseID: item.EnterpriseID },
            { $addToSet: { formData: { $each: formData } } }
          );
        }
      }
    }
  }

  async handleImportPlans(plans, grantType) {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo
      ? ctx.session.adminUserInfo.EnterpriseID
      : '';
    let noExistProducts = [];

    for (let i = 0; i < plans.length; i++) {
      const plan = plans[i];

      if (grantType === 'mill') {
        const { workspace, station, products } = plan;
        // 找到对应的车间岗位
        const aggregateParams = [{ $match: { name: workspace, EnterpriseID } }];
        if (station) {
          aggregateParams.push(
            ...[
              { $unwind: '$children' },
              { $match: { 'children.name': station } },
            ]
          );
        }
        const millDoc = await ctx.service.db.aggregate(
          'MillConstruction',
          aggregateParams
        );

        if (millDoc && millDoc.length > 0) {
          const selectChildMills = [];
          if (station) {
            const newMillDoc = millDoc[0].children;
            this.hanleSelectAllChildMill(newMillDoc, selectChildMills, []);
          } else {
            this.hanleSelectAllChildMill(millDoc[0], selectChildMills, []);
          }

          const targetMill = millDoc[0];
          const newPlan = {
            EnterpriseID,
            workspaces: targetMill._id,
            workspacesName: targetMill.name,
            workstation: targetMill.children._id,
            workstationName: targetMill.children.name,
            products: [],
            planStatus: 1,
            grantType,
            subRegion: selectChildMills,
          };

          console.log(88877766, newPlan);

          // 找库存中有的产品
          for (let j = 0; j < plan.products.length; j++) {
            const productsItem = products[j];
            const targetProduct = await this.findProductByName(
              EnterpriseID,
              productsItem.product
            );

            if (targetProduct) {
              newPlan.products.push({
                productType: [ targetProduct.name, targetProduct.product ],
                product: targetProduct.product,
                number: parseFloat(productsItem.number),
                time: productsItem.time,
                timeUnit: productsItem.timeUnit,
              });
            } else {
              noExistProducts.push(productsItem.product);
            }
          }

          // 查找是否存在该车间的发放标准
          // let doc = await ctx.model.ProtectionPlan.findOne({ workspaces: newPlan.workspaces, workstation: newPlan.workstation });
          let doc = await ctx.service.db.findOne('ProtectionPlan', {
            workspaces: newPlan.workspaces,
            workstation: newPlan.workstation,
          });

          if (doc) {
            await ctx.service.db.updateOne(
              'ProtectionPlan',
              { _id: doc._id },
              { $set: { products: newPlan.products } }
            );
            // doc = await ctx.model.ProtectionPlan.findOne({ workspaces: newPlan.workspaces, workstation: newPlan.workstation });
            doc = await ctx.service.db.findOne('ProtectionPlan', {
              workspaces: newPlan.workspaces,
              workstation: newPlan.workstation,
            });
            // 修改未领取的领用记录 - 使用新的通用方法
            await ctx.service.receiveRecord.generateRecordsForPlan(doc._id);
          } else {
            doc = await ctx.service.db.create('ProtectionPlan', newPlan);
            // 新建配发标准后生成领用记录 - 使用新的通用方法
            await ctx.service.receiveRecord.generateRecordsForPlan(doc._id);
          }
        }
      } else if (grantType === 'depart') {
        const departs = await this.findDeparts(EnterpriseID);
        // 找到对应的部门
        const { departNames, products } = plan;
        let departId = '';
        let departName = '';
        if (!departNames) {
          continue;
        }
        const departNameList = departNames.split('/');

        let parentDepart = null;
        if (departNameList.length === 1) {
          // parentDepart = await ctx.model.Dingtree.findOne({ EnterpriseID, name: departNameList[0] });
          parentDepart = await ctx.service.db.findOne('Dingtree', {
            EnterpriseID,
            name: departNameList[0],
          });
        } else if (departNameList.length > 1) {
          for (let d = 0; d < departNameList.length; d++) {
            const departName = departNameList[d];
            let departDoc = null;

            if (!parentDepart) {
              const departs = await ctx.service.db.find('Dingtree', {
                EnterpriseID,
                name: departName,
              });

              for (let z = 0; z < departs.length; z++) {
                const depart = departs[z];
                // const isHasDepart = await ctx.model.Dingtree.findOne({ EnterpriseID, name: departNameList[d + 1], parentid: depart._id });
                const isHasDepart = await ctx.service.db.findOne('Dingtree', {
                  EnterpriseID,
                  name: departNameList[d + 1],
                  parentid: depart._id,
                });
                if (isHasDepart) {
                  departDoc = depart;
                  break;
                }
              }
            } else {
              // departDoc = await ctx.model.Dingtree.findOne({ EnterpriseID, name: departName, parentid: parentDepart._id });
              departDoc = await ctx.service.db.findOne('Dingtree', {
                EnterpriseID,
                name: departName,
                parentid: parentDepart._id,
              });
            }

            if (departDoc) {
              // 循环到最后一个，也能找到，那parentDepart就赋值departDoc
              parentDepart = departDoc;
            } else {
              parentDepart = null;
              break;
            }
          }
        }

        if (parentDepart) {
          departId = parentDepart._id;
          departName = parentDepart.name;
        }

        if (departId) {
          const subRegion = [];
          this.hanleSelectAllChildDepart(departs, departId, subRegion, []);

          const newPlan = {
            EnterpriseID,
            departId,
            departName,
            products: [],
            planStatus: 1,
            grantType,
            subRegion,
          };
          // 找库存中有的产品
          for (let j = 0; j < products.length; j++) {
            const productsItem = products[j];
            const targetProduct = await this.findProductByName(
              EnterpriseID,
              productsItem.product
            );

            if (targetProduct) {
              newPlan.products.push({
                productType: [ targetProduct.name, targetProduct.product ],
                product: targetProduct.product,
                number: parseFloat(productsItem.number),
                time: productsItem.time,
                timeUnit: productsItem.timeUnit,
              });
            } else {
              noExistProducts.push(productsItem.product);
            }
          }

          // 查找是否存在该车间的发放标准
          // let doc = await ctx.model.ProtectionPlan.findOne({ departId: newPlan.departId });
          let doc = await ctx.service.db.findOne('ProtectionPlan', {
            departId: newPlan.departId,
          });
          if (doc) {
            await ctx.service.db.updateOne(
              'ProtectionPlan',
              { _id: doc._id },
              { $set: { products: newPlan.products, subRegion } }
            );
            // doc = await ctx.model.ProtectionPlan.findOne({ departId: newPlan.departId });
            // 修改未领取的领用记录 - 使用新的通用方法
            await ctx.service.receiveRecord.generateRecordsForPlan(doc._id);
          } else {
            doc = await ctx.service.db.create('ProtectionPlan', newPlan);

            // 生成领用记录 - 使用新的通用方法
            await ctx.service.receiveRecord.generateRecordsForPlan(doc._id);
          }
        }
      }
    }
    noExistProducts = Array.from(new Set(noExistProducts));
    return {
      noExistProducts,
    };
  }
  async findDeparts(EnterpriseID) {
    const { ctx } = this;
    const pipeline = [
      {
        $match: { EnterpriseID },
      },
      {
        $project: { _id: 1, parentid: 1 },
      },
    ];
    const docs = await ctx.service.db.aggregate('Dingtree', pipeline);

    return docs;
  }
  hanleSelectAllChildDepart(departs, departId, data, parentIds) {
    const targetDepart = departs.find(item => item._id === departId);
    if (targetDepart) {
      const newParentIds = parentIds.concat([ targetDepart._id ]);
      data.push(newParentIds);

      const children = departs.filter(e => e.parentid === departId);
      for (let i = 0; i < children.length; i++) {
        const item = children[i];

        this.hanleSelectAllChildDepart(departs, item._id, data, newParentIds);
      }
    }
  }
  hanleSelectAllChildMill(mill, data, parentIds = []) {
    if (mill && mill.category) {
      data.push(parentIds.concat([ mill._id ]));

      if (mill.children) {
        const newParentIds = parentIds.concat([ mill._id ]);
        for (let i = 0; i < mill.children.length; i++) {
          const child = mill.children[i];

          this.hanleSelectAllChildMill(child, data, newParentIds);
        }
      }
    }
  }

  async handlePlanToReceive(plan) {
    if (!plan) {
      return;
    }
    const { ctx } = this;
    const startDate = moment(plan.startDate || new Date());
    const warningDate = moment(startDate).add(5, 'd');
    const newPlan = {
      workshop: plan.workshop,
      workshopName: plan.workshopName,
      workspaces: plan.workspaces,
      workspacesName: plan.workspacesName,
      workstation: plan.workstation,
      workstationName: plan.workstationName,
      departId: plan.departId,
      departName: plan.departName,
      employee: plan.employee,
      planStatus: 1,
      planId: plan._id,
      startDate,
      receiveStartDate: startDate,
      warningDate,
      EnterpriseID: plan.EnterpriseID,
    };

    await ctx.service.receiveRecord.createReceiveRecordByPlan({
      plan: newPlan,
      hasCompleteEmployee: [],
      products: _.cloneDeep(plan.products),
      grantType: plan.grantType,
    });
  }

  // 配发标准发生改变时，然后增加了新的要新增，如果少了要删除，其他的更新下
  async handleChangePlanToReceive(plan) {
    const { ctx } = this;
    const pipeline = [
      { $match: { planId: plan._id } },
      { $group: { _id: '$products.product' } },
      { $unwind: '$_id' },
    ];
    const oldGroup = await ctx.service.db.aggregate('ReceiveRecord', pipeline);
    const oldGroupName = oldGroup.map(e => e._id);
    const newGroupName = plan.products.map(e => e.product);

    const needDelGroupName = oldGroupName.filter(
      e => !newGroupName.includes(e)
    );
    for (let i = 0; i < needDelGroupName.length; i++) {
      const needDelGroupNameItem = needDelGroupName[i];
      await ctx.service.db.remove('ReceiveRecord', {
        planId: plan._id,
        'products.product': needDelGroupNameItem,
        sign: { $exists: false },
      });
    }

    for (let i = 0; i < plan.products.length; i++) {
      const product = plan.products[i];

      // 更新
      if (oldGroupName.includes(product.product)) {
        // await ctx.model.ReceiveRecord.updateMany({ planId: plan._id, 'products.product': product.product, sign: { $exists: false } }, { $set: { products: [ product ] } });
        await ctx.service.db.updateMany(
          'ReceiveRecord',
          {
            planId: plan._id,
            'products.product': product.product,
            sign: { $exists: false },
          },
          { $set: { products: [ product ] } }
        );
      } else {
        // 新增
        const startDate = moment(plan.startDate || new Date());
        const warningDate = moment(startDate).add(5, 'd');
        const newPlan = {
          workshop: plan.workshop,
          workshopName: plan.workshopName,
          workspaces: plan.workspaces,
          workspacesName: plan.workspacesName,
          workstation: plan.workstation,
          workstationName: plan.workstationName,
          departId: plan.departId,
          departName: plan.departName,
          employee: plan.employee,
          planStatus: 1,
          planId: plan._id,
          startDate,
          receiveStartDate: startDate,
          warningDate,
          EnterpriseID: plan.EnterpriseID,
        };

        await ctx.service.receiveRecord.createReceiveRecordByPlan({
          plan: newPlan,
          hasCompleteEmployee: [],
          products: _.cloneDeep([ product ]),
          grantType: plan.grantType,
        });
      }
    }
  }

  async findProductByName(EnterpriseID, productName) {
    const pipeline = [
      { $match: { EnterpriseID } },
      { $unwind: '$list' },
      { $unwind: '$list.data' },
      { $match: { 'list.data.product': productName } },
      {
        $project: {
          name: '$list.name',
          product: '$list.data.product',
        },
      },
    ];
    const doc = await this.ctx.service.db.aggregate(
      'ProtectiveSuppliesList',
      pipeline
    );

    if (doc && doc[0]) {
      return doc[0];
    }
    return null;
  }
}

module.exports = DefendProductsService;
