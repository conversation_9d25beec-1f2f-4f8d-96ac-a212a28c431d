

const Service = require('egg').Service;

class ProtectionStandardsFlatService extends Service {

  /**
   * 获取扁平化的防护用品标准列表
   * @param {Object} filters 筛选条件
   * @param {Object} pagination 分页参数
   * @returns {Object} 扁平化的工种列表数据
   */
  async getFlatStandardsList(filters = {}, pagination = {}) {
    const { ctx } = this;
    const { page = 1, limit = 20 } = pagination;
    const skip = (page - 1) * limit;

    try {
      // 构建查询条件 - 只查询岗位级别的数据
      const query = {
        category: 'stations',
      };
      if (filters.fullId) {
        console.log('收到fullId筛选参数:', filters.fullId);

        // 检查选中节点的信息，判断是企业还是下级节点
        const selectedNode = await ctx.service.db.findOne(
          'FlatMillConstructionMaterialized',
          { _id: filters.fullId }
        );
        console.log('选中节点信息:', selectedNode);

        if (selectedNode) {
          const escapedFullId = filters.fullId.replace(
            /[.*+?^${}()|[\]\\]/g,
            '\\$&'
          );

          if (selectedNode.category === 'stations') {
            // 如果选中的是岗位级别，直接匹配该岗位
            query._id = filters.fullId;
            console.log('岗位级筛选，直接匹配 fullId:', filters.fullId);
          } else {
            // 如果是 mill/workspaces 级别，正则匹配 fullId 后面加下划线的模式
            // 例如：W2m1gLKXQ 匹配 W2m1gLKXQ_xxx
            query.fullId = new RegExp(`${escapedFullId}_`);
            console.log('上级节点筛选，正则匹配 fullId_xxx:', query.fullId);
          }
        } else {
          // 兜底：按企业ID筛选
          query.EnterpriseID = filters.fullId;
          console.log('兜底筛选，按企业ID:', filters.fullId);
        }
      }
      if (filters.keyword) {
        query.$or = [
          { name: new RegExp(filters.keyword, 'i') },
          { stationName: new RegExp(filters.keyword, 'i') },
          { fullPath: new RegExp(filters.keyword, 'i') },
        ];
      }

      console.log('最终查询条件:', JSON.stringify(query, null, 2));
      // 使用模型查询而不是聚合管道，确保能正确获取数据
      const pipeline = [
        // 1. 匹配工种节点
        { $match: query },

        // 2. 联查配发标准
        {
          $lookup: {
            from: 'protectionplans',
            let: { stationFullId: '$fullId' },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: [ '$nodeFullId', '$$stationFullId' ] },
                    ],
                  },
                },
              },
            ],
            as: 'protectionPlan',
          },
        },


        // 3. 添加计算字段
        {
          $addFields: {
            // 获取配发标准信息
            protectionPlanData: { $arrayElemAt: [ '$protectionPlan', 0 ] },
          },
        },

        // 5. 计算配置状态和添加existingPlanId
        {
          $addFields: {
            protectionPlans: {
              $ifNull: [ '$protectionPlanData.products', []],
            },
            // 修复Bug：添加existingPlanId字段，用于编辑时的数据更新
            existingPlanId: '$protectionPlanData._id',
            configStatus: {
              $cond: {
                // 修复Bug：与保存逻辑保持一致的状态计算
                // 1. 如果数据库中明确设置为"无需配置"，则显示"无需配置"
                if: { $eq: [ '$protectionPlanData.configStatus', 'no_need' ] },
                then: 'no_need',
                else: {
                  $cond: {
                    // 2. 如果有防护用品配置，则显示"已配置"
                    if: {
                      $and: [
                        { $ne: [ '$protectionPlanData', null ] },
                        { $gt: [{ $size: { $ifNull: [ '$protectionPlanData.products', []] } }, 0 ] },
                      ],
                    },
                    then: 'configured',
                    else: {
                      // 3. 如果没有防护用品配置且不是"无需配置"，则显示"未配置"
                      $literal: 'unconfigured'
                    },
                  },
                },
              },
            },
          },
        },

        // 6. 重新格式化数据结构
        {
          $addFields: {
            mill: '$millName',
            workshop: '$workspaceName',
            station: { $ifNull: [ '$stationName', '$name' ] },
          },
        },

        // 7. 清理临时字段
        {
          $project: {
            protectionPlan: 0,
            protectionPlanData: 0,
          },
        },

        // 8. 固定排序规则
        {
          $sort: {
            enterpriseName: 1,
            millName: 1,
            workspaceName: 1,
            stationName: 1,
            name: 1,
          },
        },
      ];

      // 应用配置状态筛选
      if (filters.configStatus) {
        pipeline.push({
          $match: {
            configStatus: filters.configStatus,
          },
        });
      }

      // 使用 $facet 同时获取总数和分页数据
      pipeline.push({
        $facet: {
          // 获取总数
          totalCount: [
            { $count: 'total' },
          ],
          // 获取分页数据
          paginatedData: [
            { $skip: skip },
            { $limit: limit },
          ],
        },
      });

      // 执行聚合查询
      const result = await ctx.service.db.aggregate('FlatMillConstructionMaterialized', pipeline);

      const facetResult = result[0];
      const total = facetResult.totalCount.length > 0 ? facetResult.totalCount[0].total : 0;
      const list = facetResult.paginatedData;

      console.log('分页信息:', { page, limit, skip, total });
      console.log('查询结果数量:', list.length);

      // 调试：检查前几条记录的existingPlanId
      if (list.length > 0) {
        console.log('前3条记录的existingPlanId调试:');
        list.slice(0, 3).forEach((item, index) => {
          console.log(`  记录${index + 1}: fullId=${item.fullId}, existingPlanId=${item.existingPlanId}, configStatus=${item.configStatus}`);
        });
      }

      return {
        list,
        total,
        currentPage: page,
        pageSize: limit,
      };

    } catch (error) {
      ctx.logger.error('获取扁平化防护用品标准列表失败:', error);
      throw error;
    }
  }

  // 注意：getOrganizationInfo 方法已删除
  // 现在直接从 FlatMillConstructionMaterialized 视图获取完整的组织架构信息
  // 包括 enterpriseName, millName, workspaceName, stationName, fullPath 等字段

  /**
   * 获取筛选选项数据
   * @returns {Object} 筛选选项
   */
  async getFilterOptions() {
    const { ctx } = this;

    try {
      const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';

      // 获取各级组织架构数据
      // 从所有工种数据中提取组织信息
      const allStations = await ctx.model.FlatMillConstructionMaterialized.find({
        EnterpriseID,
        level: 'stations',
      });

      const enterprises = new Set();
      const mills = new Set(); // 厂房
      const workshops = new Set(); // 车间
      const stations = new Set(); // 岗位

      // 从每个工种的组织信息中提取各级名称 - 直接使用视图字段
      for (const station of allStations) {
        if (station.enterpriseName) enterprises.add(station.enterpriseName);
        if (station.millName) mills.add(station.millName);
        if (station.workspaceName) workshops.add(station.workspaceName);
        if (station.stationName) stations.add(station.stationName);
      }

      // 获取防护用品类别
      const protectionCategories = await ctx.model.ProtectionPlan.distinct('products.productType', {
        EnterpriseID,
      });

      return {
        enterprises: Array.from(enterprises).filter(Boolean),
        mills: Array.from(mills).filter(Boolean), // 厂房
        workshops: Array.from(workshops).filter(Boolean), // 车间
        stations: Array.from(stations).filter(Boolean), // 岗位
        protectionCategories: protectionCategories.flat().filter(Boolean),
        configStatuses: [
          { value: 'configured', label: '已配置' },
          { value: 'unconfigured', label: '未配置' },
          { value: 'no_need', label: '无需配置' },
        ],
      };

    } catch (error) {
      ctx.logger.error('获取筛选选项失败:', error);
      throw error;
    }
  }

  /**
   * 批量更新配置状态
   * @param {Array} workStationIds 工种ID列表
   * @param {String} configStatus 配置状态
   */
  async updateConfigStatus(workStationIds, configStatus) {
    const { ctx } = this;

    try {
      const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';

      if (configStatus === 'no_need') {
        // 无需配置：删除现有计划，创建空的标记记录
        for (const nodeFullId of workStationIds) {
          // 删除现有的防护用品计划
          await ctx.model.ProtectionPlan.deleteMany({
            EnterpriseID,
            nodeFullId,
          });

          // 获取节点信息
          const nodeInfo = await ctx.model.FlatMillConstructionMaterialized.findOne({
            fullId: nodeFullId,
          }).select('name level parentId');

          // 创建一个空的标记记录
          await ctx.model.ProtectionPlan.create({
            EnterpriseID,
            nodeFullId,
            nodeLevel: nodeInfo ? nodeInfo.level : null,
            nodeName: nodeInfo ? nodeInfo.name : null,
            products: [], // 空的产品列表
            configStatus: 'no_need',
          });
        }
      } else {
        // 其他状态：直接更新配置状态
        await ctx.model.ProtectionPlan.updateMany(
          {
            EnterpriseID,
            nodeFullId: { $in: workStationIds },
          },
          {
            $set: { configStatus },
          }
        );
      }

      return { success: true, message: '配置状态更新成功' };

    } catch (error) {
      ctx.logger.error('更新配置状态失败:', error);
      throw error;
    }
  }
}

module.exports = ProtectionStandardsFlatService;
