<template>
  <el-dialog
    title="配置防护用品标准"
    :visible.sync="dialogVisible"
    width="80%"
    :before-close="handleClose"
    @close="handleClose"
  >
    <div v-if="currentWorkStation">
      <el-card style="margin-bottom: 20px;">
        <div slot="header">
          <span>工种信息</span>
        </div>
        <el-row :gutter="20">
          <el-col :span="8">
            <strong>企业：</strong>{{ currentWorkStation.enterpriseName }}
          </el-col>
          <el-col :span="12">
            <strong>工作场所：</strong>{{ currentWorkStation.fullPath }}
          </el-col>
          <el-col :span="4">
            <strong>配置状态：</strong>
            <el-tag
              :type="getStatusTagType(currentWorkStation.configStatus)"
              size="small"
            >
              {{ getStatusText(currentWorkStation.configStatus) }}
            </el-tag>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 10px;" v-if="false">
          <!-- 隐藏详细信息，保留结构以防需要 -->
          <el-col :span="6">
            <strong>工种ID：</strong>{{ currentWorkStation.fullId }}
          </el-col>
          <el-col :span="18">
            <strong>配置状态：</strong>
            <el-select v-model="currentConfigStatus" @change="handleConfigStatusChange" style="width: 150px;">
              <el-option
                v-for="item in configStatusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
                <span :style="{ color: item.color }">
                  <i :class="item.icon"></i> {{ item.label }}
                </span>
              </el-option>
            </el-select>
          </el-col>
        </el-row>
      </el-card>

      <!-- 工种配置信息展示（只读） -->
      <el-card style="margin-bottom: 20px;" v-if="currentWorkStation.harmFactors && currentWorkStation.harmFactors.length > 0">
        <div slot="header">
          <span>危害因素信息</span>
        </div>
        <div class="harmFactorInfo">
          <span>接触危害因素：</span>
          <span>{{ currentWorkStation.harmFactors.join("、") }}</span>
        </div>
      </el-card>

      <el-card>
        <div slot="header">
          <span>防护用品配置</span>
          <el-button style="float: right;" type="primary" size="small" @click="addProtectionPlan">
            <i class="el-icon-plus"></i> 新增配置
          </el-button>
        </div>
        
        <!-- 使用标签页形式展示防护用品配置，参照NewForm.vue的逻辑 -->
        <div class="tabs">
          <!-- 移除新增按钮，根据用户要求 -->
          <el-tabs
            v-model="editableTabsValue"
            type="card"
            closable
            @tab-remove="removeTab"
          >
            <el-tab-pane
              v-for="(item, index) in protectionPlans"
              :key="index"
              :label="item.product || '请选择'"
              :name="index.toString()"
            >
              <span slot="label">
                {{ item.product ? item.product : '请选择' }}
              </span>

              <el-form label-width="120px">
                <el-form-item label="防护用品">
                  <el-cascader
                    ref="protectionCascader"
                    v-model="item.productType"
                    :options="productList"
                    @change="(e) => handleChangeProtection(e, item, index)"
                    :props="cascaderProps_protection"
                    style="width: 100%"
                  >
                    <template slot-scope="{ node, data }">
                      <span v-if="data.type === 'category'">{{ data.product }}</span>
                      <span v-else>{{ data.product }}</span>
                    </template>
                  </el-cascader>
                </el-form-item>

                <el-form-item label="数量">
                  <el-input v-model="item.number" placeholder="请输入数量" />
                </el-form-item>

                <el-form-item v-if="branch === 'wh'" label="首次发放最大允许量">
                  <el-input v-model="item.maxNumber" placeholder="请输入最大允许量" />
                </el-form-item>

                <!-- 过期判断方式显示 -->
                <el-form-item label="过期判断方式" v-if="item.hasExpiry">
                  <div class="expiry-info">
                    <el-tag type="success" size="small">
                      生产日期 + {{ item.expiryPeriod }}{{ getExpiryUnitText(item.expiryUnit) }}
                    </el-tag>
                  </div>
                  <div class="form-tip" style="margin-top: 5px;">
                    发放时需记录生产日期，按产品有效期判断过期
                  </div>
                </el-form-item>

                <el-form-item label="领用周期">
                  <el-input v-model="item.time" placeholder="请输入周期">
                    <el-select
                      style="width: 100px"
                      v-model="item.timeUnit"
                      slot="append"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="timeItem in timeUnit"
                        :label="timeItem.label"
                        :value="timeItem.value"
                        :key="timeItem.value"
                      ></el-option>
                    </el-select>
                  </el-input>
                </el-form-item>

                <el-form-item label="备注">
                  <el-input
                    v-model="item.remark"
                    placeholder="请输入备注"
                    type="textarea"
                    :rows="2"
                  />
                </el-form-item>
              </el-form>
            </el-tab-pane>
          </el-tabs>
        </div>

        <div v-if="protectionPlans.length === 0" style="text-align: center; padding: 40px; color: #999;">
          <i class="el-icon-info" style="font-size: 48px; margin-bottom: 10px;"></i>
          <p>暂无防护用品配置，请点击"新增"添加</p>
        </div>
      </el-card>

      <!-- 移除无需配置时的提示，允许在任何状态下添加配置 -->
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSave" :loading="saving">保 存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { findHarmFactors, getBranch, saveSingle, updateConfigStatus } from '@/api/index';
import { getProtectionCategoryTree } from '@/api/protectionCategory';
import { mapState } from 'vuex';

export default {
  name: 'ConfigDialog',

  computed: {
    ...mapState(['branch'])
  },

  watch: {
    // 监听防护用品配置变化，自动更新配置状态
    protectionPlans: {
      handler(newPlans) {
        if (this.currentConfigStatus !== 'no_need') {
          // 如果不是"无需配置"状态，根据是否有防护用品自动更新状态
          this.currentConfigStatus = newPlans.length > 0 ? 'configured' : 'unconfigured';
        }
      },
      deep: true
    }
  },

  data() {
    return {
      dialogVisible: false,
      currentWorkStation: null,
      protectionPlans: [],
      saving: false,
      // 新增字段，参照NewForm.vue
      editableTabsValue: '0',
      currentConfigStatus: '',
      productList: [],
      cascaderProps_protection: {
        label: "product",
        value: "product",
        children: "data",
      },
      // 时间单位选项
      timeUnit: [
        { label: "天", value: "d" },
        { label: "周", value: "w" },
        { label: "月", value: "M" },
        { label: "季", value: "Q" },
        { label: "年", value: "y" },
      ],
      // 配置状态选项
      configStatusOptions: [
        {
          label: '已配置',
          value: 'configured',
          color: '#67C23A',
          icon: 'el-icon-check'
        },
        {
          label: '未配置',
          value: 'unconfigured',
          color: '#E6A23C',
          icon: 'el-icon-warning'
        },
        {
          label: '无需配置',
          value: 'no_need',
          color: '#909399',
          icon: 'el-icon-minus'
        }
      ]
    };
  },
  
  methods: {
    // 将分类树转换为productList格式，支持多级级联选择器（自适应层级）
    transformCategoryTreeToProductList(categoryTree) {
      const productList = [];

      // 递归处理分类树，构建多级级联结构
      const processCategory = (category, parentPath = '') => {
        const currentPath = parentPath ? `${parentPath}/${category.name}` : category.name;

        // 如果有子分类，创建中间节点
        if (category.children && category.children.length > 0) {
          const categoryNode = {
            product: category.name,
            type: 'category',
            data: []
          };

          // 递归处理所有子分类
          category.children.forEach(child => {
            const childResult = this.processChildCategory(child, currentPath);
            if (childResult) {
              categoryNode.data.push(childResult);
            }
          });

          // 只有当有子数据时才添加到结果中
          if (categoryNode.data.length > 0) {
            return categoryNode;
          }
        } else {
          // 叶子节点，直接返回产品信息
          return {
            product: category.name,
            categoryId: category._id,
            categoryPath: currentPath,
            categoryName: category.name,
            // 添加默认领用周期信息
            defaultCycle: category.defaultCycle || null,
            // 添加有效期相关信息
            hasExpiry: category.hasExpiry || false,
            expiryPeriod: category.expiryPeriod || null,
            expiryUnit: category.expiryUnit || 'days',
            needProductionDate: category.needProductionDate || false
          };
        }
      };

      // 处理子分类的辅助方法
      this.processChildCategory = (category, parentPath) => {
        const currentPath = `${parentPath}/${category.name}`;

        if (category.children && category.children.length > 0) {
          // 有子分类，创建中间节点
          const categoryNode = {
            product: category.name,
            type: 'category',
            data: []
          };

          category.children.forEach(child => {
            const childResult = this.processChildCategory(child, currentPath);
            if (childResult) {
              categoryNode.data.push(childResult);
            }
          });

          return categoryNode.data.length > 0 ? categoryNode : null;
        } else {
          // 叶子节点
          return {
            product: category.name,
            categoryId: category._id,
            categoryPath: currentPath,
            categoryName: category.name,
            // 添加默认领用周期信息
            defaultCycle: category.defaultCycle || null,
            // 添加有效期相关信息
            hasExpiry: category.hasExpiry || false,
            expiryPeriod: category.expiryPeriod || null,
            expiryUnit: category.expiryUnit || 'days',
            needProductionDate: category.needProductionDate || false
          };
        }
      };

      // 处理顶级分类
      categoryTree.forEach(category => {
        const result = processCategory(category);
        if (result) {
          productList.push(result);
        }
      });

      return productList;
    },

    // 加载防护用品分类数据
    async loadProductList() {
      try {
        const categoryRes = await getProtectionCategoryTree({
          includeSystem: true,
          activeOnly: true
        });
        if (categoryRes.status === 200) {
          this.productList = this.transformCategoryTreeToProductList(categoryRes.data || []);
          console.log('加载的productList数据:', this.productList);
        }
      } catch (error) {
        console.error('加载防护用品分类数据失败:', error);
      }
    },

    // 打开弹窗
    async open(workStation) {
      console.log('ConfigDialog.open 接收到的数据:', workStation);
      console.log('ConfigDialog.open existingPlanId:', workStation.existingPlanId);

      this.currentWorkStation = workStation;
      this.currentConfigStatus = workStation.configStatus || 'unconfigured';

      // 加载防护用品分类数据
      await this.loadProductList();

      // 处理protectionPlans数据 - 使用深拷贝确保数据独立性
      let protectionPlans = [];
      try {
        if (workStation.protectionPlans && Array.isArray(workStation.protectionPlans)) {
          protectionPlans = JSON.parse(JSON.stringify(workStation.protectionPlans));
          console.log('ConfigDialog - 成功加载protectionPlans数据，数量:', protectionPlans.length);
        }
      } catch (error) {
        console.error('ConfigDialog - protectionPlans数据解析失败:', error);
        protectionPlans = [];
      }

      // 处理每个防护用品配置，确保级联选择器能正确回显
      this.protectionPlans = protectionPlans.map((plan, index) => {
        console.log(`处理第${index + 1}个防护用品配置:`, plan);
        console.log('原始productType:', plan.productType);
        console.log('categoryPath:', plan.categoryPath);
        console.log('product:', plan.product);

        // 根据categoryPath重新构建productType数组以确保正确回显（支持多级自适应）
        let productType = [];
        if (plan.categoryPath) {
          // 从categoryPath构建productType数组，支持任意级别的分类
          const pathParts = plan.categoryPath.split('/').filter(part => part.trim() !== '');
          productType = [...pathParts]; // 直接使用所有路径部分
        } else if (Array.isArray(plan.productType) && plan.productType.length > 0) {
          // 兼容原有的productType数据
          productType = [...plan.productType];
        }

        console.log('重新构建的productType:', productType);

        return {
          ...plan,
          productType,
          product: plan.product || plan.categoryName || '',
          time: plan.time || '',
          timeUnit: plan.timeUnit || 'M',
          number: plan.number || 1,
          maxNumber: plan.maxNumber || '',
          remark: plan.remark || '',
          categoryId: plan.categoryId || '',
          categoryPath: plan.categoryPath || '',
          categoryName: plan.categoryName || plan.product || ''
        };
      });

      console.log('ConfigDialog - 最终处理结果，protectionPlans数量:', this.protectionPlans.length);

      // 如果有数据，默认选中第一个标签页
      if (this.protectionPlans.length > 0) {
        this.editableTabsValue = '0';
      } else {
        this.editableTabsValue = '';
      }
      this.dialogVisible = true;
    },

    // 关闭弹窗
    handleClose() {
      this.dialogVisible = false;
      this.currentWorkStation = null;
      this.protectionPlans = [];
      this.currentConfigStatus = '';
    },

    // 配置状态变化处理
    handleConfigStatusChange(status) {
      this.currentConfigStatus = status;
      // 移除清空配置的逻辑，允许在任何状态下保持已有配置
    },

    // 新增防护用品配置，参照NewForm.vue
    addProtectionPlan() {
      const newPlan = {
        product: '',
        productType: [],
        number: 1,
        time: '',
        timeUnit: 'M',
        remark: '',
        maxNumber: '',
        // 新增分类字段
        categoryId: '',
        categoryPath: '',
        categoryName: ''
      };
      this.protectionPlans.push(newPlan);
      this.editableTabsValue = (this.protectionPlans.length - 1).toString();
    },

    // 删除标签页，参照NewForm.vue
    removeTab(targetName) {
      const tabs = this.protectionPlans;
      let activeName = this.editableTabsValue;
      const targetIndex = parseInt(targetName);

      if (activeName === targetName) {
        // 如果删除的是当前激活的标签页，需要重新选择激活的标签页
        if (targetIndex < tabs.length - 1) {
          // 如果不是最后一个，选择下一个
          activeName = targetIndex.toString();
        } else if (targetIndex > 0) {
          // 如果是最后一个但不是第一个，选择前一个
          activeName = (targetIndex - 1).toString();
        } else {
          // 如果只有一个标签页，删除后没有激活的标签页
          activeName = '0';
        }
      } else {
        // 如果删除的不是当前激活的标签页，需要调整激活标签页的索引
        const currentActiveIndex = parseInt(activeName);
        if (targetIndex < currentActiveIndex) {
          activeName = (currentActiveIndex - 1).toString();
        }
      }

      this.protectionPlans.splice(targetIndex, 1);
      this.editableTabsValue = this.protectionPlans.length > 0 ? activeName : '0';
    },

    // 删除防护用品配置（保留原有方法兼容性）
    removeProtectionPlan(index) {
      this.protectionPlans.splice(index, 1);
    },


    // 防护用品选择变化处理，参照NewForm.vue
    handleChangeProtection(value, item, index) {
      if (value && value.length > 0) {
        const checkNodes = this.$refs.protectionCascader[index].getCheckedNodes()[0];

        // 检查是否选择了分类数据（新的分类系统）
        if (checkNodes.data && checkNodes.data.categoryId) {
          // 新的分类系统数据
          item.categoryId = checkNodes.data.categoryId;
          item.categoryPath = checkNodes.data.categoryPath;
          item.categoryName = checkNodes.data.categoryName;
          item.product = checkNodes.data.categoryName;

          // 从分类数据中获取有效期信息（如果有的话）
          if (checkNodes.data.hasExpiry !== undefined) {
            item.hasExpiry = checkNodes.data.hasExpiry;
            item.expiryPeriod = checkNodes.data.expiryPeriod;
            item.expiryUnit = checkNodes.data.expiryUnit;
            item.needProductionDate = checkNodes.data.needProductionDate;
          }

          // 自动从分类数据中获取默认领用周期
          if (checkNodes.data.defaultCycle) {
            item.time = checkNodes.data.defaultCycle.time || '';
            item.timeUnit = checkNodes.data.defaultCycle.timeUnit || 'M';
          }
        } else {
          // 兼容原有的防护用品清单数据
          item.product = value[value.length - 1];
        }
        item.productType = value;
      }
    },

    // 获取有效期单位文本
    getExpiryUnitText(unit) {
      const unitMap = {
        'days': '天',
        'months': '月',
        'years': '年'
      };
      return unitMap[unit] || unit;
    },

    // 获取时间单位文本
    getTimeUnitText(unit) {
      const unitMap = {
        'd': '天',
        'w': '周',
        'M': '月',
        'Q': '季',
        'y': '年'
      };
      return unitMap[unit] || unit;
    },

    // 保存配置
    async handleSave() {
      // 修复Bug2：无需配置状态的处理逻辑
      if (this.currentConfigStatus === 'no_need') {
        this.saving = true;
        try {
          const saveData = {
            nodeFullId: this.currentWorkStation.fullId,
            nodeLevel: this.currentWorkStation.level,
            nodeName: this.currentWorkStation.name,
            // 修复Bug2：无需配置状态下应该传递当前的protectionPlans，而不是空数组
            // 这样可以保存用户在无需配置状态下添加的防护用品配置
            products: this.protectionPlans,
            configStatus: this.currentConfigStatus,
            category: this.currentWorkStation.category || '',
            subRegion: [] // 子区域，根据需要设置
          };

          // 修复Bug1：确保existingPlanId正确传递，用于更新现有记录
          console.log('无需配置状态检查existingPlanId:', {
            'this.currentWorkStation.existingPlanId': this.currentWorkStation.existingPlanId,
            'typeof': typeof this.currentWorkStation.existingPlanId
          });

          // 临时解决方案：如果没有existingPlanId，尝试通过getStationDetail查询
          if (!this.currentWorkStation.existingPlanId && this.currentWorkStation.fullId) {
            console.log('无需配置状态：没有existingPlanId，尝试通过getStationDetail查询...');
            try {
              const response = await this.$http.post('/api/defendproducts/getStationDetail', {
                fullId: this.currentWorkStation.fullId
              });
              if (response.data && response.data.data && response.data.data.existingPlanId) {
                saveData._id = response.data.data.existingPlanId;
                console.log('无需配置状态：通过getStationDetail获取到existingPlanId:', response.data.data.existingPlanId);
              } else {
                console.log('无需配置状态：getStationDetail也没有返回existingPlanId，创建新记录');
              }
            } catch (error) {
              console.error('无需配置状态：查询existingPlanId失败:', error);
            }
          } else if (this.currentWorkStation.existingPlanId) {
            saveData._id = this.currentWorkStation.existingPlanId;
            console.log('更新现有配发标准（无需配置状态），ID:', this.currentWorkStation.existingPlanId);
          } else {
            console.log('创建新的配发标准（无需配置），原因：existingPlanId为', this.currentWorkStation.existingPlanId);
          }

          console.log('保存无需配置状态的数据:', saveData);
          await saveSingle(saveData);

          this.$message.success('保存成功');
          this.$emit('save', {
            workStation: {
              ...this.currentWorkStation,
              configStatus: this.currentConfigStatus,
              protectionPlans: this.protectionPlans,
              // 确保existingPlanId被正确传递
              existingPlanId: this.currentWorkStation.existingPlanId
            },
            protectionPlans: this.protectionPlans,
            configStatus: this.currentConfigStatus
          });

          this.handleClose();
        } catch (error) {
          this.$message.error('保存失败：' + error.message);
        } finally {
          this.saving = false;
        }
        return;
      }

      // 验证防护用品配置数据，参照NewForm.vue的验证逻辑
      if (this.protectionPlans.length === 0) {
        this.$message.warning('请至少添加一个防护用品配置');
        return;
      }

      let validMessage = "";
      this.protectionPlans.forEach((item, index) => {
        if (
          !item.product ||
          !item.number ||
          !item.time ||
          !item.timeUnit ||
          !item.productType ||
          item.productType.length === 0
        ) {
          validMessage = `请完善第${index + 1}个防护用品的内容`;
        }
      });

      if (validMessage) {
        this.$message.warning(validMessage);
        return;
      }

      this.saving = true;
      try {
        // 调用API保存配发标准数据
        const saveData = {
          nodeFullId: this.currentWorkStation.fullId,
          nodeLevel: this.currentWorkStation.level,
          nodeName: this.currentWorkStation.name,
          products: this.protectionPlans,
          configStatus: this.currentConfigStatus,
          category: this.currentWorkStation.category || '',
          subRegion: [] // 子区域，根据需要设置
        };

        // 修复Bug1：确保existingPlanId正确传递用于更新现有记录
        console.log('检查existingPlanId:', {
          'this.currentWorkStation.existingPlanId': this.currentWorkStation.existingPlanId,
          'typeof': typeof this.currentWorkStation.existingPlanId,
          'currentWorkStation': this.currentWorkStation
        });

        // 临时解决方案：如果没有existingPlanId，尝试通过getStationDetail查询
        if (!this.currentWorkStation.existingPlanId && this.currentWorkStation.fullId) {
          console.log('没有existingPlanId，尝试通过getStationDetail查询...');
          try {
            const response = await this.$http.post('/api/defendproducts/getStationDetail', {
              fullId: this.currentWorkStation.fullId
            });
            if (response.data && response.data.data && response.data.data.existingPlanId) {
              saveData._id = response.data.data.existingPlanId;
              console.log('通过getStationDetail获取到existingPlanId:', response.data.data.existingPlanId);
            } else {
              console.log('getStationDetail也没有返回existingPlanId，创建新记录');
            }
          } catch (error) {
            console.error('查询existingPlanId失败:', error);
          }
        } else if (this.currentWorkStation.existingPlanId) {
          saveData._id = this.currentWorkStation.existingPlanId;
          console.log('更新现有配发标准（有配置状态），ID:', this.currentWorkStation.existingPlanId);
        } else {
          console.log('创建新的配发标准，原因：existingPlanId为', this.currentWorkStation.existingPlanId);
        }

        console.log('保存的数据:', saveData);
        await saveSingle(saveData);

        // 修复Bug：根据实际配置情况确定最终状态，优先考虑防护用品配置
        let finalConfigStatus = this.currentConfigStatus;

        // 优先考虑实际的防护用品配置情况
        if (this.protectionPlans.length > 0) {
          // 有防护用品配置，优先设为"已配置"（即使用户之前设置为"无需配置"）
          finalConfigStatus = 'configured';
        } else if (this.currentConfigStatus === 'no_need') {
          // 没有防护用品配置，但用户设置为"无需配置"
          finalConfigStatus = 'no_need';
        } else {
          // 没有防护用品配置，且不是"无需配置"
          finalConfigStatus = 'unconfigured';
        }

        this.$message.success('保存成功');
        this.$emit('save', {
          workStation: {
            ...this.currentWorkStation,
            configStatus: finalConfigStatus,
            protectionPlans: this.protectionPlans,
            // 确保existingPlanId被正确传递
            existingPlanId: this.currentWorkStation.existingPlanId
          },
          protectionPlans: this.protectionPlans,
          configStatus: finalConfigStatus
        });

        this.handleClose();
      } catch (error) {
        this.$message.error('保存失败：' + error.message);
      } finally {
        this.saving = false;
      }
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const typeMap = {
        'configured': 'success',
        'unconfigured': 'warning',
        'no_need': 'info'
      };
      return typeMap[status] || '';
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        'configured': '已配置',
        'unconfigured': '未配置',
        'no_need': '无需配置'
      };
      return textMap[status] || '未知';
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}

.expiry-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

::v-deep .el-dialog__body {
  padding: 20px;
}

::v-deep .el-card__header {
  padding: 15px 20px;
  border-bottom: 1px solid #EBEEF5;
}

::v-deep .el-card__body {
  padding: 20px;
}

// 新增样式，参照NewForm.vue
.harmFactorInfo {
  margin-bottom: 10px;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-size: 14px;

  span:first-child {
    font-weight: bold;
    color: #606266;
  }
}

.tabs {
  .addBtn {
    margin-bottom: 10px;
  }

  .el-tabs {
    .el-tab-pane {
      padding: 20px 0;
    }
  }
}
</style>
